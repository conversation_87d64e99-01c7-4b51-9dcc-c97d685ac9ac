import structlog
from typing import Dict, Any
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.ollama_service import OllamaService
from app.models.social_post import SocialPost, SentimentType
from config.settings import settings

logger = structlog.get_logger()


class SentimentAgent:
    """Agent responsible for sentiment analysis of social media posts"""
    
    def __init__(self):
        self.ollama_service = OllamaService()
    
    async def analyze_post_sentiment(
        self, 
        post: SocialPost, 
        db: AsyncSession
    ) -> Dict[str, Any]:
        """Analyze sentiment of a social media post"""
        
        try:
            logger.info(f"Analyzing sentiment for post {post.id}")
            
            # Get sentiment analysis from Ollama
            sentiment_result = await self.ollama_service.analyze_sentiment(post.content)
            
            # Map sentiment to our enum
            sentiment_label = self._map_sentiment_label(sentiment_result["sentiment"])
            
            # Update post with sentiment data
            post.sentiment_score = sentiment_result["score"]
            post.sentiment = sentiment_label

            # Determine if response is required based on sentiment and settings
            post.requires_response = self._should_generate_response(
                sentiment_result["score"],
                sentiment_label
            )

            # Mark as processed
            post.processed = True
            
            # Save changes
            await db.commit()
            
            logger.info(
                f"Sentiment analysis completed for post {post.id}",
                sentiment=sentiment_label,
                score=sentiment_result["score"],
                confidence=sentiment_result["confidence"],
                requires_response=post.requires_response
            )
            
            return {
                "post_id": post.id,
                "sentiment": sentiment_label,
                "score": sentiment_result["score"],
                "confidence": sentiment_result["confidence"],
                "requires_response": post.requires_response,
                "reasoning": sentiment_result.get("reasoning", "")
            }
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment for post {post.id}: {e}")
            
            # Set fallback values
            post.sentiment_score = 0.0
            post.sentiment = SentimentType.NEUTRAL
            post.processed = True
            post.requires_response = False
            
            await db.commit()
            raise
    
    def _map_sentiment_label(self, sentiment_str: str) -> str:
        """Map sentiment string to our SentimentType enum"""
        sentiment_mapping = {
            "positive": SentimentType.POSITIVE,
            "negative": SentimentType.NEGATIVE,
            "neutral": SentimentType.NEUTRAL
        }
        return sentiment_mapping.get(sentiment_str.lower(), SentimentType.NEUTRAL)
    
    def _should_generate_response(self, sentiment_score: float, sentiment_label: str) -> bool:
        """Determine if a response should be generated based on sentiment"""
        
        # Always respond to negative sentiment (customer service)
        if sentiment_label == SentimentType.NEGATIVE:
            return True
        
        # Respond to highly positive sentiment (engagement)
        if (sentiment_label == SentimentType.POSITIVE and 
            sentiment_score >= settings.sentiment_threshold_positive):
            return True
        
        # Respond to very negative sentiment
        if sentiment_score <= settings.sentiment_threshold_negative:
            return True
        
        # Don't respond to neutral sentiment by default
        return False
    
    async def batch_analyze_posts(
        self, 
        posts: list[SocialPost], 
        db: AsyncSession
    ) -> list[Dict[str, Any]]:
        """Analyze sentiment for multiple posts"""
        
        results = []
        
        for post in posts:
            try:
                result = await self.analyze_post_sentiment(post, db)
                results.append(result)
            except Exception as e:
                logger.error(f"Failed to analyze post {post.id}: {e}")
                results.append({
                    "post_id": post.id,
                    "error": str(e),
                    "sentiment": SentimentType.NEUTRAL,
                    "score": 0.0,
                    "confidence": 0.0,
                    "requires_response": False
                })
        
        return results
