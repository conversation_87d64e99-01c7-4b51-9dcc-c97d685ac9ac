from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc
from typing import List, Optional
from datetime import datetime

from app.database import get_db
from app.models.social_post import SocialPost
from app.schemas.social_post import SocialPostResponse, SocialPostList
from app.agents.sentiment_agent import SentimentAgent

router = APIRouter()


@router.get("/", response_model=SocialPostList)
async def get_posts(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    platform: Optional[str] = Query(None),
    sentiment: Optional[str] = Query(None),
    requires_response: Optional[bool] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """Get list of social media posts with filtering"""
    
    query = select(SocialPost).order_by(desc(SocialPost.created_at))
    
    # Apply filters
    if platform:
        query = query.where(SocialPost.platform == platform)
    
    if sentiment:
        query = query.where(SocialPost.sentiment == sentiment)
    
    if requires_response is not None:
        query = query.where(SocialPost.requires_response == requires_response)
    
    # Apply pagination
    query = query.offset(skip).limit(limit)
    
    result = await db.execute(query)
    posts = result.scalars().all()
    
    # Get total count for pagination
    count_query = select(SocialPost)
    if platform:
        count_query = count_query.where(SocialPost.platform == platform)
    if sentiment:
        count_query = count_query.where(SocialPost.sentiment == sentiment)
    if requires_response is not None:
        count_query = count_query.where(SocialPost.requires_response == requires_response)
    
    count_result = await db.execute(count_query)
    total = len(count_result.scalars().all())
    
    return SocialPostList(
        posts=[SocialPostResponse.from_orm(post) for post in posts],
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/{post_id}", response_model=SocialPostResponse)
async def get_post(
    post_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Get a specific social media post"""
    
    query = select(SocialPost).where(SocialPost.id == post_id)
    result = await db.execute(query)
    post = result.scalar_one_or_none()
    
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")
    
    return SocialPostResponse.from_orm(post)


@router.post("/{post_id}/analyze-sentiment")
async def analyze_post_sentiment(
    post_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Manually trigger sentiment analysis for a post"""
    
    query = select(SocialPost).where(SocialPost.id == post_id)
    result = await db.execute(query)
    post = result.scalar_one_or_none()
    
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")
    
    # Analyze sentiment
    sentiment_agent = SentimentAgent()
    try:
        analysis_result = await sentiment_agent.analyze_post_sentiment(post, db)
        return {
            "message": "Sentiment analysis completed",
            "result": analysis_result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Sentiment analysis failed: {str(e)}")


@router.post("/analyze-all-sentiment")
async def analyze_all_sentiment(db: AsyncSession = Depends(get_db)):
    """Trigger sentiment analysis for all unprocessed posts"""

    # Get posts that need sentiment analysis
    query = select(SocialPost).where(
        SocialPost.sentiment.is_(None)
    ).limit(50)  # Process in batches
    result = await db.execute(query)
    posts = result.scalars().all()

    if not posts:
        return {
            "message": "No posts requiring sentiment analysis",
            "processed_count": 0,
            "total_posts": 0
        }

    # TODO: Trigger Celery task for batch sentiment analysis
    # from app.tasks.sentiment_analysis import process_unanalyzed_posts
    # process_unanalyzed_posts.delay()

    return {
        "message": f"Sentiment analysis triggered for {len(posts)} posts",
        "processed_count": len(posts),
        "total_posts": len(posts),
        "posts": [
            {
                "id": post.id,
                "content": post.content[:100] + "..." if len(post.content) > 100 else post.content,
                "platform": post.platform
            }
            for post in posts
        ]
    }


@router.get("/stats/sentiment")
async def get_sentiment_stats(
    platform: Optional[str] = Query(None),
    days: int = Query(7, ge=1, le=365),
    db: AsyncSession = Depends(get_db)
):
    """Get sentiment statistics for posts"""
    
    # Calculate date range
    since_date = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
    since_date = since_date.replace(day=since_date.day - days)
    
    query = select(SocialPost).where(
        SocialPost.created_at >= since_date,
        SocialPost.sentiment.isnot(None)
    )
    
    if platform:
        query = query.where(SocialPost.platform == platform)
    
    result = await db.execute(query)
    posts = result.scalars().all()
    
    # Calculate statistics
    total_posts = len(posts)
    if total_posts == 0:
        return {
            "total_posts": 0,
            "sentiment_distribution": {},
            "average_sentiment_score": 0,
            "posts_requiring_response": 0
        }
    
    sentiment_counts = {}
    sentiment_scores = []
    response_required_count = 0
    
    for post in posts:
        # Count sentiment labels
        sentiment = post.sentiment
        sentiment_counts[sentiment] = sentiment_counts.get(sentiment, 0) + 1
        
        # Collect sentiment scores
        if post.sentiment_score is not None:
            sentiment_scores.append(post.sentiment_score)
        
        # Count posts requiring response
        if post.requires_response:
            response_required_count += 1
    
    # Calculate average sentiment score
    avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0
    
    return {
        "total_posts": total_posts,
        "sentiment_distribution": sentiment_counts,
        "average_sentiment_score": round(avg_sentiment, 3),
        "posts_requiring_response": response_required_count,
        "response_rate": round(response_required_count / total_posts * 100, 1) if total_posts > 0 else 0
    }


@router.get("/stats/sentiment/public")
async def get_public_sentiment_stats(
    platform: Optional[str] = Query(None),
    days: int = Query(7, ge=1, le=365),
    db: AsyncSession = Depends(get_db)
):
    """Get sentiment statistics for posts (no authentication required)"""

    # Calculate date range
    since_date = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
    since_date = since_date.replace(day=since_date.day - days)

    query = select(SocialPost).where(
        SocialPost.created_at >= since_date,
        SocialPost.sentiment.isnot(None)
    )

    if platform:
        query = query.where(SocialPost.platform == platform)

    result = await db.execute(query)
    posts = result.scalars().all()

    # Count sentiment distribution
    sentiment_counts = {"positive": 0, "negative": 0, "neutral": 0}
    sentiment_scores = []
    response_required_count = 0

    for post in posts:
        if post.sentiment:
            sentiment_counts[post.sentiment] = sentiment_counts.get(post.sentiment, 0) + 1

        if post.sentiment_score is not None:
            sentiment_scores.append(post.sentiment_score)

        if post.requires_response:
            response_required_count += 1

    total_posts = len(posts)

    # Calculate average sentiment score
    avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0

    return {
        "total_posts": total_posts,
        "sentiment_distribution": sentiment_counts,
        "average_sentiment_score": round(avg_sentiment, 3),
        "posts_requiring_response": response_required_count,
        "response_rate": round(response_required_count / total_posts * 100, 1) if total_posts > 0 else 0
    }
