from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc
from typing import List, Optional
from datetime import datetime

from app.database import get_db
from app.models.social_post import SocialPost
from app.models.generated_response import Response, ResponseStatus
from app.schemas.generated_response import (
    ResponseResponse,
    ResponseList,
    ResponseCreate,
    ResponseApproval,
    ResponseRegeneration
)
from app.agents.response_agent import ResponseAgent
from app.agents.multi_response_agent import MultiResponseAgent

router = APIRouter()


@router.get("/", response_model=ResponseList)
async def get_responses(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = Query(None),
    platform: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """Get list of generated responses with filtering"""
    
    query = select(Response).order_by(desc(Response.created_at))

    # Apply filters
    if status:
        query = query.where(Response.status == status)

    if platform:
        query = query.join(SocialPost).where(SocialPost.platform == platform)

    # Apply pagination
    query = query.offset(skip).limit(limit)

    result = await db.execute(query)
    responses = result.scalars().all()

    # Get total count
    count_query = select(Response)
    if status:
        count_query = count_query.where(Response.status == status)
    if platform:
        count_query = count_query.join(SocialPost).where(SocialPost.platform == platform)
    
    count_result = await db.execute(count_query)
    total = len(count_result.scalars().all())
    
    return ResponseList(
        responses=[ResponseResponse.model_validate(response) for response in responses],
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/{response_id}", response_model=ResponseResponse)
async def get_response(
    response_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Get a specific generated response"""
    
    query = select(Response).where(Response.id == response_id)
    result = await db.execute(query)
    response = result.scalar_one_or_none()
    
    if not response:
        raise HTTPException(status_code=404, detail="Response not found")
    
    return ResponseResponse.model_validate(response)


@router.post("/", response_model=ResponseResponse)
async def create_response(
    response_data: ResponseCreate,
    db: AsyncSession = Depends(get_db)
):
    """Generate a new response for a social media post"""
    
    # Get the post
    query = select(SocialPost).where(SocialPost.id == response_data.social_post_id)
    result = await db.execute(query)
    post = result.scalar_one_or_none()
    
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")
    
    # Generate response
    response_agent = ResponseAgent()
    try:
        generated_response = await response_agent.generate_response(
            post=post,
            db=db,
            tone=response_data.tone,
            context=response_data.context
        )
        
        return ResponseResponse.model_validate(generated_response)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Response generation failed: {str(e)}")


@router.post("/{response_id}/approve")
async def approve_response(
    response_id: int,
    approval: ResponseApproval,
    db: AsyncSession = Depends(get_db)
):
    """Approve or reject a generated response"""
    
    query = select(Response).where(Response.id == response_id)
    result = await db.execute(query)
    response = result.scalar_one_or_none()
    
    if not response:
        raise HTTPException(status_code=404, detail="Response not found")
    
    # Update response status
    if approval.approved:
        response.status = ResponseStatus.APPROVED
    else:
        response.status = ResponseStatus.REJECTED
    
    response.admin_notes = approval.admin_notes
    response.reviewed_at = datetime.utcnow()
    
    await db.commit()
    
    # TODO: If publish_immediately is True and approved, trigger publishing
    if approval.approved and approval.publish_immediately:
        # This would trigger the publishing task
        pass
    
    return {
        "message": f"Response {'approved' if approval.approved else 'rejected'}",
        "response_id": response_id,
        "status": response.status
    }


@router.post("/{response_id}/regenerate")
async def regenerate_response(
    response_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Regenerate multiple response variants using ORM Flow multi-response system"""

    # Get the original response to find the associated post
    query = select(Response).where(Response.id == response_id)
    result = await db.execute(query)
    original_response = result.scalar_one_or_none()

    if not original_response:
        raise HTTPException(status_code=404, detail="Response not found")

    # Get the associated social post
    post_query = select(SocialPost).where(SocialPost.id == original_response.social_post_id)
    post_result = await db.execute(post_query)
    post = post_result.scalar_one_or_none()

    if not post:
        raise HTTPException(status_code=404, detail="Associated post not found")

    # Generate multiple response variants using ORM Flow
    multi_response_agent = MultiResponseAgent()
    try:
        response_batch = await multi_response_agent.generate_multiple_responses(
            post=post,
            db=db,
            context=f"Regenerated from response ID {response_id}"
        )

        await db.commit()

        # Prepare response variants for UI
        response_variants = []
        for response in response_batch.responses:
            response_variants.append({
                "id": response.id,
                "content": response.content,
                "tone": response.tone,
                "confidence_score": response.confidence_score,
                "sentiment_analysis": response.sentiment_analysis,
                "variant_number": response.response_variant_number
            })

        return {
            "success": True,
            "batch_id": response_batch.id,
            "total_variants": response_batch.total_variants,
            "requires_airline_approval": response_batch.requires_airline_approval,
            "response_variants": response_variants,
            "original_response_id": response_id,
            "message": "Multiple response variants generated successfully. Please review in the ORM Flow dashboard."
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Response regeneration failed: {str(e)}")


@router.get("/pending/count")
async def get_pending_count(db: AsyncSession = Depends(get_db)):
    """Get count of pending responses"""
    
    query = select(Response).where(Response.status == ResponseStatus.PENDING)
    result = await db.execute(query)
    pending_responses = result.scalars().all()
    
    return {
        "pending_count": len(pending_responses),
        "total_responses": len(pending_responses)  # This would be total in a real implementation
    }


@router.get("/stats/performance")
async def get_performance_stats(
    days: int = Query(7, ge=1, le=365),
    db: AsyncSession = Depends(get_db)
):
    """Get performance statistics for generated responses"""
    
    # Calculate date range
    since_date = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
    since_date = since_date.replace(day=since_date.day - days)
    
    query = select(Response).where(Response.created_at >= since_date)
    result = await db.execute(query)
    responses = result.scalars().all()
    
    if not responses:
        return {
            "total_responses": 0,
            "status_distribution": {},
            "average_confidence": 0,
            "approval_rate": 0
        }
    
    # Calculate statistics
    total_responses = len(responses)
    status_counts = {}
    confidence_scores = []
    approved_count = 0
    
    for response in responses:
        # Count status distribution
        status = response.status
        status_counts[status] = status_counts.get(status, 0) + 1
        
        # Collect confidence scores
        if response.confidence_score is not None:
            confidence_scores.append(response.confidence_score)
        
        # Count approved responses
        if response.status == ResponseStatus.APPROVED:
            approved_count += 1
    
    # Calculate averages
    avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
    approval_rate = approved_count / total_responses * 100 if total_responses > 0 else 0
    
    return {
        "total_responses": total_responses,
        "status_distribution": status_counts,
        "average_confidence": round(avg_confidence, 3),
        "approval_rate": round(approval_rate, 1)
    }


@router.post("/generate-all")
async def generate_all_responses(db: AsyncSession = Depends(get_db)):
    """Generate responses for all posts that require them"""

    # Get posts that need responses but don't have them yet
    query = select(SocialPost).where(
        SocialPost.requires_response == True,
        ~SocialPost.id.in_(
            select(Response.social_post_id).where(Response.status.in_(["pending", "approved"]))
        )
    ).limit(20)  # Process in batches
    result = await db.execute(query)
    posts = result.scalars().all()

    if not posts:
        return {
            "message": "No posts requiring response generation",
            "generated_count": 0,
            "total_posts": 0
        }

    # TODO: Trigger Celery task for batch response generation
    # from app.tasks.response_generation import generate_pending_responses
    # generate_pending_responses.delay()

    return {
        "message": f"Response generation triggered for {len(posts)} posts",
        "generated_count": len(posts),
        "total_posts": len(posts),
        "posts": [
            {
                "id": post.id,
                "content": post.content[:100] + "..." if len(post.content) > 100 else post.content,
                "platform": post.platform,
                "sentiment": post.sentiment
            }
            for post in posts
        ]
    }


@router.post("/publish-approved")
async def publish_approved_responses(db: AsyncSession = Depends(get_db)):
    """Publish all approved responses to social media"""

    # Get approved responses that haven't been published
    query = select(Response).where(
        Response.status == "approved",
        Response.published_at.is_(None)
    ).limit(10)  # Process in batches
    result = await db.execute(query)
    responses = result.scalars().all()

    if not responses:
        return {
            "message": "No approved responses ready for publishing",
            "published_count": 0,
            "total_responses": 0
        }

    # TODO: Trigger Celery task for publishing
    # from app.tasks.publishing import publish_approved_responses
    # publish_approved_responses.delay()

    return {
        "message": f"Publishing triggered for {len(responses)} approved responses",
        "published_count": len(responses),
        "total_responses": len(responses),
        "responses": [
            {
                "id": response.id,
                "response_text": response.response_text[:100] + "..." if len(response.response_text) > 100 else response.response_text,
                "social_post_id": response.social_post_id,
                "confidence_score": response.confidence_score
            }
            for response in responses
        ]
    }
