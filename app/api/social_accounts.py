from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List

from app.database import get_db
from app.models.social_account import SocialAccount
from app.schemas.social_account import (
    SocialAccountResponse,
    SocialAccountCreate,
    SocialAccountUpdate
)

router = APIRouter()


@router.get("/", response_model=List[SocialAccountResponse])
async def get_social_accounts(db: AsyncSession = Depends(get_db)):
    """Get all social media accounts"""
    
    query = select(SocialAccount).order_by(SocialAccount.created_at.desc())
    result = await db.execute(query)
    accounts = result.scalars().all()
    
    return [SocialAccountResponse.from_orm(account) for account in accounts]


@router.get("/{account_id}", response_model=SocialAccountResponse)
async def get_social_account(
    account_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Get a specific social media account"""
    
    query = select(SocialAccount).where(SocialAccount.id == account_id)
    result = await db.execute(query)
    account = result.scalar_one_or_none()
    
    if not account:
        raise HTTPException(status_code=404, detail="Social account not found")
    
    return SocialAccountResponse.from_orm(account)


@router.post("/", response_model=SocialAccountResponse)
async def create_social_account(
    account_data: SocialAccountCreate,
    db: AsyncSession = Depends(get_db)
):
    """Create a new social media account"""
    
    # Check if account already exists
    query = select(SocialAccount).where(
        SocialAccount.platform == account_data.platform,
        SocialAccount.username == account_data.username
    )
    result = await db.execute(query)
    existing_account = result.scalar_one_or_none()
    
    if existing_account:
        raise HTTPException(
            status_code=400, 
            detail="Account already exists for this platform and username"
        )
    
    # Create new account
    account = SocialAccount(**account_data.model_dump())
    db.add(account)
    await db.commit()
    await db.refresh(account)
    
    return SocialAccountResponse.from_orm(account)


@router.put("/{account_id}", response_model=SocialAccountResponse)
async def update_social_account(
    account_id: int,
    account_data: SocialAccountUpdate,
    db: AsyncSession = Depends(get_db)
):
    """Update a social media account"""
    
    query = select(SocialAccount).where(SocialAccount.id == account_id)
    result = await db.execute(query)
    account = result.scalar_one_or_none()
    
    if not account:
        raise HTTPException(status_code=404, detail="Social account not found")
    
    # Update account fields
    update_data = account_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(account, field, value)
    
    await db.commit()
    await db.refresh(account)
    
    return SocialAccountResponse.from_orm(account)


@router.delete("/{account_id}")
async def delete_social_account(
    account_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Delete a social media account"""
    
    query = select(SocialAccount).where(SocialAccount.id == account_id)
    result = await db.execute(query)
    account = result.scalar_one_or_none()
    
    if not account:
        raise HTTPException(status_code=404, detail="Social account not found")
    
    await db.delete(account)
    await db.commit()
    
    return {"message": "Social account deleted successfully"}


@router.post("/{account_id}/sync")
async def sync_account_posts(
    account_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Manually trigger sync for a social media account"""
    
    query = select(SocialAccount).where(SocialAccount.id == account_id)
    result = await db.execute(query)
    account = result.scalar_one_or_none()
    
    if not account:
        raise HTTPException(status_code=404, detail="Social account not found")
    
    # TODO: Trigger sync task
    # This would typically be done via Celery task
    
    return {
        "message": "Sync triggered for account",
        "account_id": account_id,
        "platform": account.platform,
        "username": account.username
    }


@router.post("/sync-all")
async def sync_all_accounts(db: AsyncSession = Depends(get_db)):
    """Manually trigger sync for all active social media accounts"""

    # Get all active accounts
    query = select(SocialAccount).where(SocialAccount.is_active == True)
    result = await db.execute(query)
    accounts = result.scalars().all()

    if not accounts:
        return {
            "message": "No active accounts found",
            "synced_accounts": 0,
            "total_accounts": 0
        }

    # TODO: Trigger Celery task for all accounts
    # from app.tasks.social_sync import sync_all_accounts
    # sync_all_accounts.delay()

    return {
        "message": f"Sync triggered for {len(accounts)} active accounts",
        "synced_accounts": len(accounts),
        "total_accounts": len(accounts),
        "accounts": [
            {
                "id": account.id,
                "platform": account.platform,
                "username": account.username
            }
            for account in accounts
        ]
    }
