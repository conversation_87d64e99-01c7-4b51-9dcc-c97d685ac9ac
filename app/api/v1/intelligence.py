"""
Deep Intelligence API endpoints for contextual analysis
Provides flight analytics, customer insights, and policy intelligence
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Dict, Any, Optional
import structlog

from app.database import get_db
from app.models.social_post import SocialPost
from app.intelligence.contextual_intelligence_engine import ContextualIntelligenceEngine
from app.agents.enhanced_response_agent import EnhancedResponseAgent

logger = structlog.get_logger()
router = APIRouter()

# Global instances
intelligence_engine = ContextualIntelligenceEngine()
enhanced_agent = EnhancedResponseAgent()


@router.get("/capabilities")
async def get_intelligence_capabilities():
    """Get information about deep intelligence capabilities"""
    return {
        "success": True,
        "capabilities": {
            "flight_analytics": {
                "real_time_status": True,
                "historical_performance": True,
                "delay_pattern_analysis": True,
                "route_specific_insights": True
            },
            "customer_intelligence": {
                "loyalty_tier_detection": True,
                "interaction_history": True,
                "preference_learning": True,
                "lifetime_value_calculation": True
            },
            "policy_engine": {
                "dynamic_compensation": True,
                "legal_compliance": True,
                "precedent_analysis": True,
                "escalation_triggers": True
            },
            "contextual_analysis": {
                "entity_extraction": True,
                "incident_classification": True,
                "urgency_assessment": True,
                "personalization": True
            }
        }
    }


@router.post("/analyze/{post_id}")
async def analyze_post_intelligence(
    post_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Perform deep contextual analysis on a specific post"""
    try:
        # Get the post
        query = select(SocialPost).where(SocialPost.id == post_id)
        result = await db.execute(query)
        post = result.scalar_one_or_none()
        
        if not post:
            raise HTTPException(status_code=404, detail="Post not found")
        
        # Initialize intelligence engine if needed
        if not hasattr(intelligence_engine, 'initialized'):
            await intelligence_engine.initialize()
            intelligence_engine.initialized = True
        
        # Perform deep analysis
        analysis = await intelligence_engine.analyze_post_context(post, db)
        
        return {
            "success": True,
            "post_id": post_id,
            "analysis": analysis,
            "timestamp": post.created_at.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error analyzing post intelligence {post_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate-intelligent-response/{post_id}")
async def generate_intelligent_response(
    post_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Generate response using full contextual intelligence"""
    try:
        # Get the post
        query = select(SocialPost).where(SocialPost.id == post_id)
        result = await db.execute(query)
        post = result.scalar_one_or_none()
        
        if not post:
            raise HTTPException(status_code=404, detail="Post not found")
        
        # Initialize agents if needed
        if not hasattr(enhanced_agent, 'initialized'):
            await enhanced_agent.initialize()
            enhanced_agent.initialized = True
        
        # Generate intelligent response
        response_result = await enhanced_agent.generate_multimodal_response(post, db)
        
        return {
            "success": response_result.get("success", False),
            "post_id": post_id,
            "response_text": response_result.get("response", ""),
            "confidence": response_result.get("confidence", 0.0),
            "contextual_intelligence": response_result.get("contextual_intelligence", {}),
            "multimodal_analysis": response_result.get("multimodal_analysis"),
            "image_context": response_result.get("image_context", "")
        }
        
    except Exception as e:
        logger.error(f"Error generating intelligent response for post {post_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/flight-analytics/{flight_number}")
async def get_flight_analytics(
    flight_number: str,
    route: Optional[str] = None
):
    """Get comprehensive flight analytics and insights"""
    try:
        if not hasattr(intelligence_engine, 'initialized'):
            await intelligence_engine.initialize()
            intelligence_engine.initialized = True
        
        # Get flight context
        flight_context = await intelligence_engine._analyze_flight_context(
            flight_number, route
        )
        
        # Get additional analytics
        historical_data = await intelligence_engine._get_historical_flight_performance(flight_number)
        delay_patterns = await intelligence_engine._analyze_delay_patterns(flight_number, route)
        
        return {
            "success": True,
            "flight_number": flight_number,
            "current_status": {
                "status": flight_context.current_status,
                "delay_minutes": flight_context.delay_minutes,
                "scheduled_departure": flight_context.scheduled_departure.isoformat() if flight_context.scheduled_departure else None,
                "actual_departure": flight_context.actual_departure.isoformat() if flight_context.actual_departure else None,
                "gate": flight_context.gate,
                "terminal": flight_context.terminal,
                "aircraft_type": flight_context.aircraft_type
            },
            "performance_analytics": {
                "on_time_30d": flight_context.on_time_percentage_30d,
                "on_time_90d": flight_context.on_time_percentage_90d,
                "performance_rating": "excellent" if flight_context.on_time_percentage_30d > 0.85 else 
                                   "good" if flight_context.on_time_percentage_30d > 0.75 else
                                   "below_average" if flight_context.on_time_percentage_30d > 0.65 else "poor"
            },
            "delay_insights": {
                "common_reasons": flight_context.common_delay_reasons or [],
                "patterns": delay_patterns
            },
            "recommendations": _generate_flight_recommendations(flight_context)
        }
        
    except Exception as e:
        logger.error(f"Error getting flight analytics for {flight_number}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/customer-insights/{username}")
async def get_customer_insights(
    username: str,
    db: AsyncSession = Depends(get_db)
):
    """Get comprehensive customer insights and history"""
    try:
        if not hasattr(intelligence_engine, 'initialized'):
            await intelligence_engine.initialize()
            intelligence_engine.initialized = True
        
        # Get customer context
        customer_context = await intelligence_engine._analyze_customer_context(username, db)
        
        # Get additional insights
        query = select(SocialPost).where(SocialPost.author_username == username)
        result = await db.execute(query)
        posts = result.scalars().all()
        
        # Analyze interaction patterns
        sentiment_distribution = {}
        for post in posts:
            sentiment = post.sentiment or "neutral"
            sentiment_distribution[sentiment] = sentiment_distribution.get(sentiment, 0) + 1
        
        return {
            "success": True,
            "username": username,
            "customer_profile": {
                "loyalty_tier": customer_context.loyalty_tier,
                "lifetime_value": customer_context.lifetime_value,
                "total_interactions": len(posts),
                "last_interaction": customer_context.last_interaction_date.isoformat() if customer_context.last_interaction_date else None
            },
            "interaction_history": {
                "total_posts": len(posts),
                "complaints": customer_context.previous_complaints,
                "compliments": customer_context.previous_compliments,
                "sentiment_distribution": sentiment_distribution
            },
            "preferences": {
                "preferred_resolution": customer_context.preferred_resolution_type,
                "communication_style": _determine_communication_style(posts),
                "response_urgency": _determine_response_urgency(customer_context)
            },
            "recommendations": _generate_customer_recommendations(customer_context, posts)
        }
        
    except Exception as e:
        logger.error(f"Error getting customer insights for {username}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/policy-analysis")
async def analyze_policy_context(
    incident_type: str,
    flight_number: Optional[str] = None,
    customer_tier: str = "basic",
    delay_minutes: int = 0,
    route: Optional[str] = None
):
    """Analyze applicable policies and compensation rules"""
    try:
        if not hasattr(intelligence_engine, 'initialized'):
            await intelligence_engine.initialize()
            intelligence_engine.initialized = True
        
        # Create mock post for analysis
        from app.models.social_post import SocialPost
        from datetime import datetime
        
        mock_post = SocialPost(
            id=0,
            content=f"Issue with {incident_type}",
            platform="twitter",
            author_username="test_user",
            created_at=datetime.now()
        )
        
        # Create contexts
        flight_context = None
        if flight_number:
            flight_context = await intelligence_engine._analyze_flight_context(flight_number, route)
            flight_context.delay_minutes = delay_minutes
        
        customer_context = intelligence_engine.CustomerContext(
            username="test_user",
            loyalty_tier=customer_tier
        )
        
        entities = {"incident_type": incident_type, "flight_number": flight_number}
        
        # Analyze policy context
        policy_context = await intelligence_engine._analyze_policy_context(
            mock_post, flight_context, customer_context, entities
        )
        
        return {
            "success": True,
            "incident_type": incident_type,
            "applicable_policies": policy_context.applicable_policies,
            "compensation": {
                "amount": policy_context.compensation_amount,
                "type": policy_context.compensation_type,
                "currency": "USD"
            },
            "legal_requirements": policy_context.legal_requirements or [],
            "escalation": {
                "threshold": policy_context.escalation_threshold,
                "recommended": policy_context.escalation_threshold in ["low", "immediate"]
            },
            "next_steps": _generate_policy_next_steps(policy_context, incident_type)
        }
        
    except Exception as e:
        logger.error(f"Error analyzing policy context: {e}")
        raise HTTPException(status_code=500, detail=str(e))


def _generate_flight_recommendations(flight_context) -> list:
    """Generate recommendations based on flight performance"""
    recommendations = []
    
    if flight_context.delay_minutes > 120:
        recommendations.append("Offer meal vouchers and hotel accommodation")
        recommendations.append("Proactively communicate rebooking options")
    
    if flight_context.on_time_percentage_30d < 0.7:
        recommendations.append("Acknowledge route performance challenges")
        recommendations.append("Offer alternative flight options")
    
    if flight_context.common_delay_reasons:
        recommendations.append(f"Address common issues: {', '.join(flight_context.common_delay_reasons[:2])}")
    
    return recommendations


def _determine_communication_style(posts) -> str:
    """Determine customer's preferred communication style"""
    if len(posts) > 5:
        return "detailed"  # Experienced customers want details
    else:
        return "concise"  # New customers prefer simple explanations


def _determine_response_urgency(customer_context) -> str:
    """Determine how urgently customer expects responses"""
    if customer_context.loyalty_tier in ["platinum", "gold"]:
        return "immediate"
    elif customer_context.previous_complaints > 3:
        return "high"
    else:
        return "standard"


def _generate_customer_recommendations(customer_context, posts) -> list:
    """Generate customer-specific recommendations"""
    recommendations = []
    
    if customer_context.loyalty_tier in ["platinum", "gold"]:
        recommendations.append("Use premium service language and priority handling")
    
    if customer_context.previous_complaints > 2:
        recommendations.append("Escalate to senior agent for personalized attention")
        recommendations.append("Reference commitment to improving their experience")
    
    if len(posts) > 10:
        recommendations.append("Acknowledge their loyalty and long-term relationship")
    
    return recommendations


def _generate_policy_next_steps(policy_context, incident_type) -> list:
    """Generate next steps based on policy analysis"""
    steps = []
    
    if policy_context.compensation_amount > 0:
        steps.append(f"Process compensation of ${policy_context.compensation_amount:.0f}")
    
    if policy_context.escalation_threshold in ["low", "immediate"]:
        steps.append("Escalate to senior customer service representative")
    
    if incident_type == "flight_delay":
        steps.append("Provide rebooking assistance")
        steps.append("Offer meal and accommodation vouchers if applicable")
    
    return steps
