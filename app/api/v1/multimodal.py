"""
Multimodal API endpoints for handling posts with images
"""

from fastapi import APIRout<PERSON>, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List, Dict, Any, Optional
import structlog

from app.database import get_db
from app.models.social_post import SocialPost
from app.models.generated_response import Response
from app.agents.enhanced_response_agent import EnhancedResponseAgent
from app.agents.multimodal_agent import MultimodalAgent

logger = structlog.get_logger()
router = APIRouter()

# Global agent instances
enhanced_agent = EnhancedResponseAgent()
multimodal_agent = MultimodalAgent()


@router.get("/capabilities")
async def get_multimodal_capabilities():
    """Get information about multimodal processing capabilities"""
    try:
        capabilities = await enhanced_agent.get_multimodal_capabilities()
        return {
            "success": True,
            "capabilities": capabilities
        }
    except Exception as e:
        logger.error(f"Error getting capabilities: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/analyze-post/{post_id}")
async def analyze_post_with_images(
    post_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Analyze a specific post that contains images"""
    try:
        # Get the post
        query = select(SocialPost).where(SocialPost.id == post_id)
        result = await db.execute(query)
        post = result.scalar_one_or_none()
        
        if not post:
            raise HTTPException(status_code=404, detail="Post not found")
        
        if not post.media_urls:
            return {
                "success": False,
                "message": "Post contains no images",
                "has_images": False
            }
        
        # Initialize agents if needed
        if not multimodal_agent.model:
            await multimodal_agent.initialize()
        
        # Analyze the post
        analysis_result = await multimodal_agent.process_post_with_images(post, db)
        
        return {
            "success": True,
            "post_id": post_id,
            "analysis": analysis_result,
            "image_count": len(post.media_urls),
            "media_urls": post.media_urls
        }
        
    except Exception as e:
        logger.error(f"Error analyzing post {post_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate-response/{post_id}")
async def generate_multimodal_response(
    post_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Generate a response for a post considering both text and images"""
    try:
        # Get the post
        query = select(SocialPost).where(SocialPost.id == post_id)
        result = await db.execute(query)
        post = result.scalar_one_or_none()
        
        if not post:
            raise HTTPException(status_code=404, detail="Post not found")
        
        # Initialize agents if needed
        if not enhanced_agent.multimodal_agent.model:
            await enhanced_agent.initialize()
        
        # Generate response
        response_result = await enhanced_agent.generate_multimodal_response(post, db)
        
        if response_result.get("success"):
            # Create Response record
            generated_response = Response(
                social_post_id=post.id,
                response_text=response_result.get("response", ""),
                confidence_score=0.8,  # You might want to calculate this based on analysis
                model_version="enhanced-multimodal-v1",
                processing_metadata={
                    "has_images": bool(post.media_urls),
                    "image_count": len(post.media_urls) if post.media_urls else 0,
                    "multimodal_analysis": response_result.get("multimodal_analysis"),
                    "image_context": response_result.get("image_context", "")
                }
            )
            
            db.add(generated_response)
            await db.commit()
            await db.refresh(generated_response)
            
            return {
                "success": True,
                "response_id": generated_response.id,
                "response_text": generated_response.response_text,
                "confidence": generated_response.confidence_score,
                "has_images": bool(post.media_urls),
                "image_analysis": response_result.get("multimodal_analysis")
            }
        else:
            raise HTTPException(
                status_code=500, 
                detail=f"Failed to generate response: {response_result.get('error')}"
            )
        
    except Exception as e:
        logger.error(f"Error generating response for post {post_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/batch-analyze")
async def batch_analyze_posts_with_images(
    post_ids: List[int],
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """Analyze multiple posts with images in the background"""
    try:
        # Get posts
        query = select(SocialPost).where(SocialPost.id.in_(post_ids))
        result = await db.execute(query)
        posts = result.scalars().all()
        
        if not posts:
            raise HTTPException(status_code=404, detail="No posts found")
        
        # Filter posts that have images
        posts_with_images = [post for post in posts if post.media_urls]
        
        if not posts_with_images:
            return {
                "success": False,
                "message": "No posts with images found",
                "total_posts": len(posts),
                "posts_with_images": 0
            }
        
        # Add background task for processing
        background_tasks.add_task(
            _process_posts_background,
            [post.id for post in posts_with_images],
            db
        )
        
        return {
            "success": True,
            "message": f"Started processing {len(posts_with_images)} posts with images",
            "total_posts": len(posts),
            "posts_with_images": len(posts_with_images),
            "processing_started": True
        }
        
    except Exception as e:
        logger.error(f"Error starting batch analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/posts-with-images")
async def get_posts_with_images(
    limit: int = 50,
    skip: int = 0,
    db: AsyncSession = Depends(get_db)
):
    """Get posts that contain images"""
    try:
        # Query posts with media_urls
        query = (
            select(SocialPost)
            .where(SocialPost.media_urls.isnot(None))
            .offset(skip)
            .limit(limit)
            .order_by(SocialPost.created_at.desc())
        )
        
        result = await db.execute(query)
        posts = result.scalars().all()
        
        # Count total posts with images
        count_query = select(SocialPost).where(SocialPost.media_urls.isnot(None))
        count_result = await db.execute(count_query)
        total = len(count_result.scalars().all())
        
        posts_data = []
        for post in posts:
            posts_data.append({
                "id": post.id,
                "content": post.content,
                "author_username": post.author_username,
                "platform": post.platform,
                "media_urls": post.media_urls,
                "image_count": len(post.media_urls) if post.media_urls else 0,
                "created_at": post.created_at,
                "sentiment_label": post.sentiment,
                "has_response": bool(post.responses)
            })
        
        return {
            "success": True,
            "posts": posts_data,
            "total": total,
            "skip": skip,
            "limit": limit
        }
        
    except Exception as e:
        logger.error(f"Error getting posts with images: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/image-analysis/{post_id}")
async def get_image_analysis(
    post_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Get detailed image analysis for a specific post"""
    try:
        # Get the post
        query = select(SocialPost).where(SocialPost.id == post_id)
        result = await db.execute(query)
        post = result.scalar_one_or_none()
        
        if not post:
            raise HTTPException(status_code=404, detail="Post not found")
        
        if not post.media_urls:
            return {
                "success": False,
                "message": "Post contains no images"
            }
        
        # Get existing responses to check for multimodal analysis
        responses_query = select(Response).where(
            Response.social_post_id == post_id
        )
        responses_result = await db.execute(responses_query)
        responses = responses_result.scalars().all()
        
        # Look for multimodal analysis in processing metadata
        multimodal_data = None
        for response in responses:
            if response.processing_metadata and "multimodal_analysis" in response.processing_metadata:
                multimodal_data = response.processing_metadata["multimodal_analysis"]
                break
        
        if multimodal_data:
            return {
                "success": True,
                "post_id": post_id,
                "image_analysis": multimodal_data,
                "cached": True
            }
        else:
            # Perform fresh analysis
            if not multimodal_agent.model:
                await multimodal_agent.initialize()
            
            analysis_result = await multimodal_agent.process_post_with_images(post, db)
            
            return {
                "success": True,
                "post_id": post_id,
                "image_analysis": analysis_result.get("analysis"),
                "cached": False
            }
        
    except Exception as e:
        logger.error(f"Error getting image analysis for post {post_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _process_posts_background(post_ids: List[int], db: AsyncSession):
    """Background task to process posts with images"""
    try:
        logger.info(f"Starting background processing of {len(post_ids)} posts")
        
        # Initialize agents
        if not enhanced_agent.multimodal_agent.model:
            await enhanced_agent.initialize()
        
        # Get posts
        query = select(SocialPost).where(SocialPost.id.in_(post_ids))
        result = await db.execute(query)
        posts = result.scalars().all()
        
        # Process each post
        results = await enhanced_agent.batch_process_multimodal_posts(posts, db)
        
        logger.info(f"Completed background processing: {len(results)} posts processed")
        
    except Exception as e:
        logger.error(f"Error in background processing: {e}")


@router.post("/regenerate-with-images/{response_id}")
async def regenerate_response_with_images(
    response_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Regenerate a response considering image content"""
    try:
        # Get the response and associated post
        response_query = select(Response).where(Response.id == response_id)
        response_result = await db.execute(response_query)
        response = response_result.scalar_one_or_none()
        
        if not response:
            raise HTTPException(status_code=404, detail="Response not found")
        
        post_query = select(SocialPost).where(SocialPost.id == response.social_post_id)
        post_result = await db.execute(post_query)
        post = post_result.scalar_one_or_none()
        
        if not post:
            raise HTTPException(status_code=404, detail="Associated post not found")
        
        # Initialize agents if needed
        if not enhanced_agent.multimodal_agent.model:
            await enhanced_agent.initialize()
        
        # Generate new response
        new_response_result = await enhanced_agent.generate_multimodal_response(post, db)
        
        if new_response_result.get("success"):
            # Update existing response
            response.response_text = new_response_result.get("response", "")
            response.model_version = "enhanced-multimodal-v1"
            response.processing_metadata = {
                "has_images": bool(post.media_urls),
                "image_count": len(post.media_urls) if post.media_urls else 0,
                "multimodal_analysis": new_response_result.get("multimodal_analysis"),
                "image_context": new_response_result.get("image_context", ""),
                "regenerated": True
            }
            
            await db.commit()
            
            return {
                "success": True,
                "response_id": response.id,
                "new_response_text": response.response_text,
                "has_images": bool(post.media_urls),
                "image_analysis": new_response_result.get("multimodal_analysis")
            }
        else:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to regenerate response: {new_response_result.get('error')}"
            )
        
    except Exception as e:
        logger.error(f"Error regenerating response {response_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
