"""
Orchestration API endpoints for n8n workflow integration
Implements the ORM Flow workflow steps as API endpoints
"""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Dict, Any, List
import structlog

from app.database import get_db
from app.models.social_post import SocialPost
from app.models.generated_response import Response
from app.models.response_batch import ResponseBatch, BatchStatus
from app.agents.sentiment_agent import SentimentAgent
from app.agents.multi_response_agent import MultiResponseAgent
from app.orchestration.n8n_client import n8n_client
from app.schemas.orchestration import (
    OrchestrationRequest,
    OrchestrationResponse,
    AgentResponse,
    ManualReviewRequest,
    ApprovalRequest
)

logger = structlog.get_logger()
router = APIRouter(prefix="/orchestration", tags=["orchestration"])


@router.post("/agent-1-monitor", response_model=OrchestrationResponse)
async def agent_1_monitor(
    request: OrchestrationRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Agent 1 - Monitoring Step
    Processes incoming social media posts and determines if response is needed
    """
    try:
        logger.info("Agent 1 - Monitor step triggered", post_id=request.post_id)
        
        # Get the social post
        result = await db.execute(select(SocialPost).where(SocialPost.id == request.post_id))
        post = result.scalar_one_or_none()
        
        if not post:
            raise HTTPException(status_code=404, detail="Social post not found")
        
        # Determine if response is needed based on post content and sentiment
        needs_response = await _determine_response_needed(post)
        
        # Update post metadata
        post.needs_response = needs_response
        post.processing_status = "monitored"
        
        await db.commit()
        
        response_data = {
            "post_id": post.id,
            "content": post.content,
            "platform": post.platform,
            "author": post.author_username,
            "needs_response": needs_response,
            "next_step": "agent-2-sentiment" if needs_response else "complete"
        }
        
        logger.info(f"Agent 1 completed - needs_response: {needs_response}", post_id=post.id)
        
        return OrchestrationResponse(
            success=True,
            message="Agent 1 monitoring completed",
            data=response_data,
            next_step="agent-2-sentiment" if needs_response else "complete"
        )
        
    except Exception as e:
        logger.error(f"Agent 1 monitor error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/agent-2-sentiment", response_model=OrchestrationResponse)
async def agent_2_sentiment(
    request: OrchestrationRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Agent 2 - Sentiment Analysis Step
    Analyzes sentiment and determines response requirements
    """
    try:
        logger.info("Agent 2 - Sentiment Analysis step triggered", post_id=request.post_id)
        
        # Get the social post
        result = await db.execute(select(SocialPost).where(SocialPost.id == request.post_id))
        post = result.scalar_one_or_none()
        
        if not post:
            raise HTTPException(status_code=404, detail="Social post not found")
        
        # Perform sentiment analysis
        sentiment_agent = SentimentAgent()
        sentiment_result = await sentiment_agent.analyze_sentiment(post, db)
        
        # Update post with sentiment analysis
        post.sentiment = sentiment_result.sentiment
        post.sentiment_score = sentiment_result.score
        post.sentiment_confidence = sentiment_result.confidence
        post.processing_status = "sentiment_analyzed"
        
        await db.commit()
        
        response_data = {
            "post_id": post.id,
            "sentiment": sentiment_result.sentiment,
            "sentiment_score": sentiment_result.score,
            "confidence": sentiment_result.confidence,
            "requires_response": sentiment_result.requires_response,
            "next_step": "agent-3-response" if sentiment_result.requires_response else "complete"
        }
        
        logger.info(f"Agent 2 completed - sentiment: {sentiment_result.sentiment}", post_id=post.id)
        
        return OrchestrationResponse(
            success=True,
            message="Agent 2 sentiment analysis completed",
            data=response_data,
            next_step="agent-3-response" if sentiment_result.requires_response else "complete"
        )
        
    except Exception as e:
        logger.error(f"Agent 2 sentiment error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/agent-3-response", response_model=OrchestrationResponse)
async def agent_3_response(
    request: OrchestrationRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Agent 3 - Response Generation Step
    Generates multiple response variants for manual review
    """
    try:
        logger.info("Agent 3 - Response Generation step triggered", post_id=request.post_id)
        
        # Get the social post
        result = await db.execute(select(SocialPost).where(SocialPost.id == request.post_id))
        post = result.scalar_one_or_none()
        
        if not post:
            raise HTTPException(status_code=404, detail="Social post not found")
        
        # Generate multiple response variants
        multi_response_agent = MultiResponseAgent()
        response_batch = await multi_response_agent.generate_multiple_responses(
            post, db, context=request.context
        )
        
        # Update post processing status
        post.processing_status = "responses_generated"
        await db.commit()
        
        # Prepare response data with all variants
        response_variants = []
        for response in response_batch.responses:
            response_variants.append({
                "id": response.id,
                "content": response.content,
                "tone": response.tone,
                "confidence_score": response.confidence_score,
                "sentiment_analysis": response.sentiment_analysis,
                "variant_number": response.response_variant_number
            })
        
        response_data = {
            "post_id": post.id,
            "batch_id": response_batch.id,
            "total_variants": response_batch.total_variants,
            "response_variants": response_variants,
            "requires_airline_approval": response_batch.requires_airline_approval,
            "next_step": "manual-review"
        }
        
        logger.info(f"Agent 3 completed - generated {len(response_variants)} variants", 
                   post_id=post.id, batch_id=response_batch.id)
        
        return OrchestrationResponse(
            success=True,
            message="Agent 3 response generation completed",
            data=response_data,
            next_step="manual-review"
        )
        
    except Exception as e:
        logger.error(f"Agent 3 response error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/manual-review", response_model=OrchestrationResponse)
async def manual_review_cresent(
    request: ManualReviewRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Manual Review - Cresent User Step
    Handles response selection and initial approval by Cresent user
    """
    try:
        logger.info("Manual Review - Cresent User step triggered", batch_id=request.batch_id)
        
        # Get the response batch
        result = await db.execute(select(ResponseBatch).where(ResponseBatch.id == request.batch_id))
        batch = result.scalar_one_or_none()
        
        if not batch:
            raise HTTPException(status_code=404, detail="Response batch not found")
        
        # If response is selected, mark it
        if request.selected_response_id:
            multi_response_agent = MultiResponseAgent()
            await multi_response_agent.select_response_variant(
                request.batch_id, request.selected_response_id, db
            )
            
            # Update batch with Cresent user approval
            batch.cresent_user_approved = request.approved
            batch.cresent_user_notes = request.notes
            batch.reviewed_by_cresent_user_id = request.reviewer_user_id
            
            if request.approved:
                if batch.requires_airline_approval:
                    batch.batch_status = BatchStatus.UNDER_REVIEW
                    next_step = "airline-approval"
                else:
                    batch.batch_status = BatchStatus.APPROVED
                    next_step = "post-response"
            else:
                batch.batch_status = BatchStatus.REJECTED
                next_step = "complete"
            
            await db.commit()
        
        response_data = {
            "batch_id": batch.id,
            "post_id": batch.social_post_id,
            "selected_response_id": batch.selected_response_id,
            "cresent_approved": batch.cresent_user_approved,
            "requires_airline_approval": batch.requires_airline_approval,
            "next_step": next_step if request.selected_response_id else "pending_selection"
        }
        
        logger.info(f"Manual Review - Cresent completed", batch_id=batch.id, approved=request.approved)
        
        return OrchestrationResponse(
            success=True,
            message="Manual review by Cresent user completed",
            data=response_data,
            next_step=next_step if request.selected_response_id else "pending_selection"
        )
        
    except Exception as e:
        logger.error(f"Manual review error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/airline-approval", response_model=OrchestrationResponse)
async def airline_approval(
    request: ApprovalRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Manual Review - Airlines User Step
    Handles final approval by airline user for sensitive responses
    """
    try:
        logger.info("Airline Approval step triggered", batch_id=request.batch_id)
        
        # Get the response batch
        result = await db.execute(select(ResponseBatch).where(ResponseBatch.id == request.batch_id))
        batch = result.scalar_one_or_none()
        
        if not batch:
            raise HTTPException(status_code=404, detail="Response batch not found")
        
        # Update batch with airline approval
        batch.airline_user_approved = request.approved
        batch.airline_user_notes = request.notes
        batch.reviewed_by_airline_user_id = request.reviewer_user_id
        
        if request.approved and batch.cresent_user_approved:
            batch.batch_status = BatchStatus.APPROVED
            next_step = "post-response"
        else:
            batch.batch_status = BatchStatus.REJECTED
            next_step = "complete"
        
        await db.commit()
        
        response_data = {
            "batch_id": batch.id,
            "post_id": batch.social_post_id,
            "airline_approved": batch.airline_user_approved,
            "final_approval": batch.is_ready_for_posting,
            "next_step": next_step
        }
        
        logger.info(f"Airline Approval completed", batch_id=batch.id, approved=request.approved)
        
        return OrchestrationResponse(
            success=True,
            message="Airline approval completed",
            data=response_data,
            next_step=next_step
        )
        
    except Exception as e:
        logger.error(f"Airline approval error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _determine_response_needed(post: SocialPost) -> bool:
    """Determine if a social media post needs a response"""
    # Keywords that typically require responses
    response_keywords = [
        "help", "support", "problem", "issue", "complaint", "question",
        "delay", "cancellation", "refund", "baggage", "customer service"
    ]
    
    content_lower = post.content.lower()
    
    # Check for mentions or direct messages
    if post.is_mention or post.is_direct_message:
        return True
    
    # Check for response keywords
    if any(keyword in content_lower for keyword in response_keywords):
        return True
    
    # Check sentiment - respond to negative sentiment
    if post.sentiment == "negative":
        return True
    
    return False
