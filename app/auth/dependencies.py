"""
FastAPI Authentication Dependencies
Integrates with the existing user management system for session-based authentication
"""

from fastapi import Depends, HTTPException, status, Request
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Optional, List
import structlog
import sys
import os

# Add the project root to the path to import user_management
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from user_management import UserManager
    USER_MANAGEMENT_AVAILABLE = True
except ImportError:
    USER_MANAGEMENT_AVAILABLE = False

from app.database import get_db
from app.models.user import User, UserRole

logger = structlog.get_logger()

# Security scheme for Bearer token
security = HTTPBearer(auto_error=False)


class AuthenticationError(HTTPException):
    """Custom authentication error"""
    def __init__(self, detail: str = "Authentication required"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"},
        )


class AuthorizationError(HTTPException):
    """Custom authorization error"""
    def __init__(self, detail: str = "Insufficient permissions"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
        )


async def get_current_user_from_session(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """
    Get current user from session token using the existing user management system
    
    Args:
        credentials: HTTP Bearer token credentials
        db: Database session
        
    Returns:
        User: The authenticated user
        
    Raises:
        AuthenticationError: If authentication fails
    """
    if not credentials:
        raise AuthenticationError("Authentication token required")
    
    session_token = credentials.credentials
    
    if not USER_MANAGEMENT_AVAILABLE:
        # Fallback: try to get user from database directly
        logger.warning("User management system not available, using fallback authentication")
        # For development/testing, create a mock admin user
        query = select(User).where(User.username == "admin")
        result = await db.execute(query)
        user = result.scalar_one_or_none()
        
        if not user:
            raise AuthenticationError("User management system unavailable")
        return user
    
    # Use the existing user management system
    try:
        user_manager = UserManager()
        user_data = user_manager.get_user_by_session(session_token)
        
        if not user_data:
            raise AuthenticationError("Invalid or expired session token")
        
        # Get the full user object from the database
        query = select(User).where(User.username == user_data['username'])
        result = await db.execute(query)
        user = result.scalar_one_or_none()
        
        if not user:
            # User exists in session but not in FastAPI database
            # This might happen during migration - create user record
            logger.info(f"Creating user record for {user_data['username']}")
            user = User(
                username=user_data['username'],
                email=user_data.get('email', f"{user_data['username']}@example.com"),
                full_name=user_data.get('full_name', user_data['username']),
                hashed_password="migrated_from_legacy",  # Placeholder
                role=user_data.get('role', UserRole.MODERATOR),
                is_active=user_data.get('is_active', True)
            )
            db.add(user)
            await db.commit()
            await db.refresh(user)
        
        # Update user role if it changed in the legacy system
        if user.role != user_data.get('role'):
            user.role = user_data.get('role', user.role)
            await db.commit()
            await db.refresh(user)
        
        return user
        
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise AuthenticationError("Authentication failed")


async def get_current_user(
    user: User = Depends(get_current_user_from_session)
) -> User:
    """
    Get current authenticated user
    
    Args:
        user: User from session authentication
        
    Returns:
        User: The authenticated user
        
    Raises:
        AuthenticationError: If user is inactive
    """
    if user.status != "active":
        raise AuthenticationError("User account is inactive")
    
    return user


async def require_admin(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Require admin role for access
    
    Args:
        current_user: The authenticated user
        
    Returns:
        User: The authenticated admin user
        
    Raises:
        AuthorizationError: If user is not admin
    """
    if current_user.role != UserRole.ADMIN:
        raise AuthorizationError("Admin access required")
    
    return current_user


async def require_cresent_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Require Cresent User role for ORM Flow access
    
    Args:
        current_user: The authenticated user
        
    Returns:
        User: The authenticated Cresent user
        
    Raises:
        AuthorizationError: If user is not Cresent User or Admin
    """
    if current_user.role not in [UserRole.CRESENT_USER, UserRole.ADMIN]:
        raise AuthorizationError("Cresent User access required")
    
    return current_user


async def require_airlines_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Require Airlines User role for ORM Flow access
    
    Args:
        current_user: The authenticated user
        
    Returns:
        User: The authenticated Airlines user
        
    Raises:
        AuthorizationError: If user is not Airlines User or Admin
    """
    if current_user.role not in [UserRole.AIRLINES_USER, UserRole.ADMIN]:
        raise AuthorizationError("Airlines User access required")
    
    return current_user


async def require_orm_flow_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Require ORM Flow user role (Cresent or Airlines User)
    
    Args:
        current_user: The authenticated user
        
    Returns:
        User: The authenticated ORM Flow user
        
    Raises:
        AuthorizationError: If user doesn't have ORM Flow access
    """
    if current_user.role not in [UserRole.CRESENT_USER, UserRole.AIRLINES_USER, UserRole.ADMIN]:
        raise AuthorizationError("ORM Flow access required")
    
    return current_user


def require_roles(allowed_roles: List[UserRole]):
    """
    Create a dependency that requires one of the specified roles
    
    Args:
        allowed_roles: List of allowed user roles
        
    Returns:
        Dependency function that checks user role
    """
    async def role_checker(current_user: User = Depends(get_current_user)) -> User:
        if current_user.role not in allowed_roles:
            role_names = [role.value for role in allowed_roles]
            raise AuthorizationError(f"Access requires one of: {', '.join(role_names)}")
        return current_user
    
    return role_checker


# Convenience dependencies for common role combinations
require_moderator_or_admin = require_roles([UserRole.MODERATOR, UserRole.ADMIN])
require_any_orm_flow_role = require_roles([UserRole.CRESENT_USER, UserRole.AIRLINES_USER, UserRole.ADMIN])


async def get_optional_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> Optional[User]:
    """
    Get current user if authenticated, otherwise return None
    Useful for endpoints that work with or without authentication
    
    Args:
        credentials: HTTP Bearer token credentials
        db: Database session
        
    Returns:
        Optional[User]: The authenticated user or None
    """
    if not credentials:
        return None
    
    try:
        return await get_current_user_from_session(credentials, db)
    except AuthenticationError:
        return None
