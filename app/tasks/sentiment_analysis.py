import structlog
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.tasks.celery import celery
from app.database import AsyncSessionLocal
from app.models.social_post import SocialPost
from app.agents.sentiment_agent import SentimentAgent

logger = structlog.get_logger()


@celery.task(bind=True, max_retries=3)
def process_unanalyzed_posts(self):
    """Process posts that haven't been analyzed for sentiment"""
    
    import asyncio
    return asyncio.run(_process_unanalyzed_posts_async())


async def _process_unanalyzed_posts_async():
    """Async implementation of process_unanalyzed_posts"""
    
    async with AsyncSessionLocal() as db:
        try:
            # Get unprocessed posts
            query = select(SocialPost).where(
                SocialPost.processed == False,
                SocialPost.sentiment.is_(None)
            ).limit(50)  # Process in batches
            
            result = await db.execute(query)
            posts = result.scalars().all()
            
            if not posts:
                logger.info("No unanalyzed posts found")
                return {"processed": 0}
            
            # Process sentiment analysis
            sentiment_agent = SentimentAgent()
            results = await sentiment_agent.batch_analyze_posts(posts, db)
            
            processed_count = len([r for r in results if "error" not in r])
            error_count = len([r for r in results if "error" in r])
            
            logger.info(f"Sentiment analysis completed: {processed_count} processed, {error_count} errors")
            
            return {
                "processed": processed_count,
                "errors": error_count,
                "total": len(posts)
            }
            
        except Exception as e:
            logger.error(f"Error in process_unanalyzed_posts: {e}")
            raise


@celery.task(bind=True, max_retries=3)
def analyze_single_post(self, post_id: int):
    """Analyze sentiment for a single post"""
    
    import asyncio
    return asyncio.run(_analyze_single_post_async(post_id))


async def _analyze_single_post_async(post_id: int):
    """Async implementation of analyze_single_post"""
    
    async with AsyncSessionLocal() as db:
        try:
            # Get the post
            query = select(SocialPost).where(SocialPost.id == post_id)
            result = await db.execute(query)
            post = result.scalar_one_or_none()
            
            if not post:
                raise ValueError(f"Post {post_id} not found")
            
            # Analyze sentiment
            sentiment_agent = SentimentAgent()
            result = await sentiment_agent.analyze_post_sentiment(post, db)
            
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing post {post_id}: {e}")
            raise
