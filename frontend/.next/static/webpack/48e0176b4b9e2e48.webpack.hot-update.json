{"c": ["webpack"], "r": ["pages/social-accounts", "pages/analytics", "pages/admin/users", "pages/admin/credentials", "pages/admin/system"], "m": ["./node_modules/lucide-react/dist/esm/icons/facebook.js", "./node_modules/lucide-react/dist/esm/icons/globe.js", "./node_modules/lucide-react/dist/esm/icons/instagram.js", "./node_modules/lucide-react/dist/esm/icons/linkedin.js", "./node_modules/lucide-react/dist/esm/icons/pen-square.js", "./node_modules/lucide-react/dist/esm/icons/trash-2.js", "./node_modules/lucide-react/dist/esm/icons/twitter.js", "./node_modules/lucide-react/dist/esm/icons/x-circle.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Edit&from=default&as=default&join=../esm/icons/pen-square!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Facebook&from=default&as=default&join=../esm/icons/facebook!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Globe&from=default&as=default&join=../esm/icons/globe!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Instagram&from=default&as=default&join=../esm/icons/instagram!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Linkedin&from=default&as=default&join=../esm/icons/linkedin!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Trash2&from=default&as=default&join=../esm/icons/trash-2!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Twitter&from=default&as=default&join=../esm/icons/twitter!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=XCircle&from=default&as=default&join=../esm/icons/x-circle!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fkarthikhari%2FDocuments%2Faugment-projects%2FAgentic%20Social%20Handler%2Ffrontend%2Fsrc%2Fpages%2Fsocial-accounts.tsx&page=%2Fsocial-accounts!", "./src/pages/social-accounts.tsx", "./node_modules/lucide-react/dist/esm/icons/download.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Download&from=default&as=default&join=../esm/icons/download!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fkarthikhari%2FDocuments%2Faugment-projects%2FAgentic%20Social%20Handler%2Ffrontend%2Fsrc%2Fpages%2Fanalytics.tsx&page=%2Fanalytics!", "./src/pages/analytics.tsx", "./node_modules/lucide-react/dist/esm/icons/more-horizontal.js", "./node_modules/lucide-react/dist/esm/icons/user-plus.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=MoreHorizontal&from=default&as=default&join=../esm/icons/more-horizontal!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=UserPlus&from=default&as=default&join=../esm/icons/user-plus!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fkarthikhari%2FDocuments%2Faugment-projects%2FAgentic%20Social%20Handler%2Ffrontend%2Fsrc%2Fpages%2Fadmin%2Fusers.tsx&page=%2Fadmin%2Fusers!", "./src/pages/admin/users.tsx", "./node_modules/lucide-react/dist/esm/icons/database.js", "./node_modules/lucide-react/dist/esm/icons/key.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Database&from=default&as=default&join=../esm/icons/database!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Key&from=default&as=default&join=../esm/icons/key!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fkarthikhari%2FDocuments%2Faugment-projects%2FAgentic%20Social%20Handler%2Ffrontend%2Fsrc%2Fpages%2Fadmin%2Fcredentials.tsx&page=%2Fadmin%2Fcredentials!", "./src/pages/admin/credentials.tsx", "./node_modules/lucide-react/dist/esm/icons/play.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Play&from=default&as=default&join=../esm/icons/play!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fkarthikhari%2FDocuments%2Faugment-projects%2FAgentic%20Social%20Handler%2Ffrontend%2Fsrc%2Fpages%2Fadmin%2Fsystem.tsx&page=%2Fadmin%2Fsystem!", "./src/pages/admin/system.tsx"]}