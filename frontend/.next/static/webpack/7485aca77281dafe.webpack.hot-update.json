{"c": ["pages/agents", "webpack"], "r": ["pages/index"], "m": ["./node_modules/@kurkle/color/dist/color.esm.js", "./node_modules/chart.js/dist/chart.js", "./node_modules/chart.js/dist/chunks/helpers.dataset.js", "./node_modules/lucide-react/dist/esm/icons/cpu.js", "./node_modules/lucide-react/dist/esm/icons/lock.js", "./node_modules/lucide-react/dist/esm/icons/plus.js", "./node_modules/lucide-react/dist/esm/icons/shield-check.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Cpu&from=default&as=default&join=../esm/icons/cpu!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Lock&from=default&as=default&join=../esm/icons/lock!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Plus&from=default&as=default&join=../esm/icons/plus!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=ShieldCheck&from=default&as=default&join=../esm/icons/shield-check!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fkarthikhari%2FDocuments%2Faugment-projects%2FAgentic%20Social%20Handler%2Ffrontend%2Fsrc%2Fpages%2Findex.tsx&page=%2F!", "./node_modules/react-chartjs-2/dist/index.js", "./src/pages/index.tsx"]}