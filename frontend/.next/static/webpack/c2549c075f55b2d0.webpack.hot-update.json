{"c": ["webpack"], "r": ["pages/response-management"], "m": ["./node_modules/lucide-react/dist/esm/icons/clock.js", "./node_modules/lucide-react/dist/esm/icons/eye.js", "./node_modules/lucide-react/dist/esm/icons/filter.js", "./node_modules/lucide-react/dist/esm/icons/message-square.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Clock&from=default&as=default&join=../esm/icons/clock!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Eye&from=default&as=default&join=../esm/icons/eye!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Filter&from=default&as=default&join=../esm/icons/filter!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=MessageSquare&from=default&as=default&join=../esm/icons/message-square!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fkarthikhari%2FDocuments%2Faugment-projects%2FAgentic%20Social%20Handler%2Ffrontend%2Fsrc%2Fpages%2Fresponse-management.tsx&page=%2Fresponse-management!", "./src/pages/response-management.tsx"]}