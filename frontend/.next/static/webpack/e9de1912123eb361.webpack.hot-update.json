{"c": ["pages/admin/users", "webpack"], "r": ["/_error", "pages/agents", "pages/response-management"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fkarthikhari%2FDocuments%2Faugment-projects%2FAgentic%20Social%20Handler%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fpages%2F_error.js&page=%2F_error!", "./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js", "./node_modules/lucide-react/dist/esm/icons/clock.js", "./node_modules/lucide-react/dist/esm/icons/eye.js", "./node_modules/lucide-react/dist/esm/icons/message-square.js", "./node_modules/lucide-react/dist/esm/icons/zap.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=BarChart3&from=default&as=default&join=../esm/icons/bar-chart-3!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Clock&from=default&as=default&join=../esm/icons/clock!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Eye&from=default&as=default&join=../esm/icons/eye!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=MessageSquare&from=default&as=default&join=../esm/icons/message-square!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Zap&from=default&as=default&join=../esm/icons/zap!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fkarthikhari%2FDocuments%2Faugment-projects%2FAgentic%20Social%20Handler%2Ffrontend%2Fsrc%2Fpages%2Fagents.tsx&page=%2Fagents!", "./src/pages/agents.tsx", "./node_modules/lucide-react/dist/esm/icons/filter.js", "./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Filter&from=default&as=default&join=../esm/icons/filter!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fkarthikhari%2FDocuments%2Faugment-projects%2FAgentic%20Social%20Handler%2Ffrontend%2Fsrc%2Fpages%2Fresponse-management.tsx&page=%2Fresponse-management!", "./src/pages/response-management.tsx"]}