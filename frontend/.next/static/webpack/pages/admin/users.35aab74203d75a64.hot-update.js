"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/users",{

/***/ "./src/pages/admin/users.tsx":
/*!***********************************!*\
  !*** ./src/pages/admin/users.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/Navigation */ \"./src/components/Navigation.tsx\");\n/* harmony import */ var modularize_import_loader_name_Users_from_default_as_default_join_esm_icons_users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! modularize-import-loader?name=Users&from=default&as=default&join=../esm/icons/users!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Users&from=default&as=default&join=../esm/icons/users!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_UserPlus_from_default_as_default_join_esm_icons_user_plus_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! modularize-import-loader?name=UserPlus&from=default&as=default&join=../esm/icons/user-plus!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=UserPlus&from=default&as=default&join=../esm/icons/user-plus!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Search_from_default_as_default_join_esm_icons_search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! modularize-import-loader?name=Search&from=default&as=default&join=../esm/icons/search!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Search&from=default&as=default&join=../esm/icons/search!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_MoreHorizontal_from_default_as_default_join_esm_icons_more_horizontal_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! modularize-import-loader?name=MoreHorizontal&from=default&as=default&join=../esm/icons/more-horizontal!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=MoreHorizontal&from=default&as=default&join=../esm/icons/more-horizontal!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Edit_from_default_as_default_join_esm_icons_pen_square_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! modularize-import-loader?name=Edit&from=default&as=default&join=../esm/icons/pen-square!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Edit&from=default&as=default&join=../esm/icons/pen-square!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Trash2_from_default_as_default_join_esm_icons_trash_2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! modularize-import-loader?name=Trash2&from=default&as=default&join=../esm/icons/trash-2!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Trash2&from=default&as=default&join=../esm/icons/trash-2!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Shield_from_default_as_default_join_esm_icons_shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! modularize-import-loader?name=Shield&from=default&as=default&join=../esm/icons/shield!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Shield&from=default&as=default&join=../esm/icons/shield!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Settings_from_default_as_default_join_esm_icons_settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! modularize-import-loader?name=Settings&from=default&as=default&join=../esm/icons/settings!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Settings&from=default&as=default&join=../esm/icons/settings!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_RefreshCw_from_default_as_default_join_esm_icons_refresh_cw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! modularize-import-loader?name=RefreshCw&from=default&as=default&join=../esm/icons/refresh-cw!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=RefreshCw&from=default&as=default&join=../esm/icons/refresh-cw!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction UserManagement() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [roleFilter, setRoleFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"All Roles\");\n    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [editingUser, setEditingUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const userStats = {\n        total: users.length,\n        admin: users.filter((u)=>u.role === \"admin\").length,\n        infra: users.filter((u)=>u.role === \"cresent_user\").length,\n        marketing: users.filter((u)=>u.role === \"airlines_user\").length\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        loadUsers();\n    }, []);\n    const loadUsers = async ()=>{\n        try {\n            setLoading(true);\n            // Try to fetch real users from API\n            try {\n                const response = await fetch(\"/api/v1/admin/users\", {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"session_token\"))\n                    }\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    setUsers(data.users || []);\n                    return;\n                }\n            } catch (e) {\n                console.log(\"Users API not available, using mock data\");\n            }\n            // Fallback to mock data\n            const mockUsers = [\n                {\n                    id: \"1\",\n                    username: \"admin\",\n                    email: \"<EMAIL>\",\n                    full_name: \"System Administrator\",\n                    role: \"admin\",\n                    status: \"active\",\n                    created_at: \"2024-01-01T00:00:00Z\",\n                    last_login: \"2024-01-08T10:30:00Z\",\n                    assigned_accounts: 5\n                },\n                {\n                    id: \"2\",\n                    username: \"cresent_reviewer\",\n                    email: \"<EMAIL>\",\n                    full_name: \"Cresent Reviewer\",\n                    role: \"cresent_user\",\n                    status: \"active\",\n                    created_at: \"2024-01-02T00:00:00Z\",\n                    last_login: \"2024-01-08T09:15:00Z\",\n                    assigned_accounts: 3\n                },\n                {\n                    id: \"3\",\n                    username: \"airline_approver\",\n                    email: \"<EMAIL>\",\n                    full_name: \"Airline Approver\",\n                    role: \"airlines_user\",\n                    status: \"active\",\n                    created_at: \"2024-01-03T00:00:00Z\",\n                    last_login: \"2024-01-07T16:45:00Z\",\n                    assigned_accounts: 2\n                }\n            ];\n            setUsers(mockUsers);\n        } catch (error) {\n            console.error(\"Failed to load users:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createUser = async (userData)=>{\n        try {\n            const response = await fetch(\"/api/v1/admin/users\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"session_token\"))\n                },\n                body: JSON.stringify(userData)\n            });\n            if (response.ok) {\n                alert(\"User created successfully!\");\n                loadUsers();\n                setShowCreateModal(false);\n            } else {\n                alert(\"Failed to create user\");\n            }\n        } catch (error) {\n            console.error(\"Error creating user:\", error);\n            alert(\"Error creating user\");\n        }\n    };\n    const deleteUser = async (userId)=>{\n        if (!confirm(\"Are you sure you want to delete this user?\")) return;\n        try {\n            const response = await fetch(\"/api/v1/admin/users/\".concat(userId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"session_token\"))\n                }\n            });\n            if (response.ok) {\n                alert(\"User deleted successfully!\");\n                loadUsers();\n            } else {\n                alert(\"Failed to delete user\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting user:\", error);\n            alert(\"Error deleting user\");\n        }\n    };\n    const editUser = async (userId)=>{\n        // Find the user to edit\n        const user = users.find((u)=>u.id === userId);\n        if (!user) return;\n        setEditingUser(user);\n        setShowEditModal(true);\n    };\n    const updateUser = async (userData)=>{\n        if (!editingUser) return;\n        try {\n            const response = await fetch(\"/api/v1/admin/users/\".concat(editingUser.id), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"session_token\"))\n                },\n                body: JSON.stringify(userData)\n            });\n            if (response.ok) {\n                alert(\"User updated successfully!\");\n                setShowEditModal(false);\n                setEditingUser(null);\n                loadUsers();\n            } else {\n                alert(\"Failed to update user\");\n            }\n        } catch (error) {\n            console.error(\"Error updating user:\", error);\n            alert(\"Error updating user\");\n        }\n    };\n    const filteredUsers = users.filter((user)=>{\n        const matchesSearch = user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) || user.username.toLowerCase().includes(searchTerm.toLowerCase()) || user.email.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesRole = roleFilter === \"All Roles\" || user.role === roleFilter;\n        return matchesSearch && matchesRole;\n    });\n    const getRoleColor = (role)=>{\n        switch(role){\n            case \"admin\":\n                return \"bg-purple-100 text-purple-800\";\n            case \"cresent_user\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"airlines_user\":\n                return \"bg-green-100 text-green-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"active\":\n                return \"bg-green-100 text-green-800\";\n            case \"inactive\":\n                return \"bg-red-100 text-red-800\";\n            case \"pending\":\n                return \"bg-yellow-100 text-yellow-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        className: \"jsx-a858199ca2dfb051\",\n                        children: \"User Management - Agentic ORM\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Manage users and their access permissions\",\n                        className: \"jsx-a858199ca2dfb051\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"a858199ca2dfb051\",\n                children: \".card.jsx-a858199ca2dfb051{background:white;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;-webkit-box-shadow:0 1px 3px 0 rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px 0 rgba(0,0,0,.1);box-shadow:0 1px 3px 0 rgba(0,0,0,.1);border:1px solid#e5e7eb;padding:1.5rem}.btn-primary.jsx-a858199ca2dfb051{background:#2563eb;color:white;font-weight:500;padding:.5rem 1rem;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;-webkit-transition:all.2s;-moz-transition:all.2s;-o-transition:all.2s;transition:all.2s;border:none;cursor:pointer}.btn-primary.jsx-a858199ca2dfb051:hover{background:#1d4ed8}.app-container.jsx-a858199ca2dfb051{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;min-height:100vh}.app-body.jsx-a858199ca2dfb051{margin-top:5rem;min-height:-webkit-calc(100vh - 5rem);min-height:-moz-calc(100vh - 5rem);min-height:calc(100vh - 5rem)}.main-content.jsx-a858199ca2dfb051{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;background:#f9fafb;width:100%}.stat-card.jsx-a858199ca2dfb051{background:white;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;padding:1.5rem;border:1px solid#e5e7eb;-webkit-box-shadow:0 1px 3px 0 rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px 0 rgba(0,0,0,.1);box-shadow:0 1px 3px 0 rgba(0,0,0,.1)}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-a858199ca2dfb051\" + \" \" + \"app-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        currentPage: \"admin\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"app-body\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"main-content\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-a858199ca2dfb051\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-2xl font-bold text-gray-900 mb-2\",\n                                                children: \"User Management\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-gray-600\",\n                                                children: \"Manage users and their access permissions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-a858199ca2dfb051\" + \" \" + \"stat-card\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"p-2 bg-blue-100 rounded-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Users_from_default_as_default_join_esm_icons_users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-5 h-5 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-sm text-gray-500\",\n                                                                    children: \"Total Users\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-2xl font-bold\",\n                                                                    children: userStats.total\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-a858199ca2dfb051\" + \" \" + \"stat-card\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"p-2 bg-green-100 rounded-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Shield_from_default_as_default_join_esm_icons_shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"w-5 h-5 text-green-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-sm text-gray-500\",\n                                                                    children: \"Admin Users\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 308,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-2xl font-bold\",\n                                                                    children: userStats.admin\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-a858199ca2dfb051\" + \" \" + \"stat-card\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"p-2 bg-purple-100 rounded-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Users_from_default_as_default_join_esm_icons_users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-5 h-5 text-purple-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-sm text-gray-500\",\n                                                                    children: \"Infra Users\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-2xl font-bold\",\n                                                                    children: userStats.infra\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-a858199ca2dfb051\" + \" \" + \"stat-card\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"p-2 bg-orange-100 rounded-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Users_from_default_as_default_join_esm_icons_users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-5 h-5 text-orange-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-sm text-gray-500\",\n                                                                    children: \"Marketing Users\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-2xl font-bold\",\n                                                                    children: userStats.marketing\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"card mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-lg font-semibold text-gray-900\",\n                                                    children: \"All Users\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex flex-col sm:flex-row gap-4 w-full sm:w-auto\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Search_from_default_as_default_join_esm_icons_search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    placeholder: \"Search users...\",\n                                                                    value: searchTerm,\n                                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"border border-gray-300 rounded-lg px-3 py-2 text-sm w-64\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: roleFilter,\n                                                            onChange: (e)=>setRoleFilter(e.target.value),\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"border border-gray-300 rounded-lg px-3 py-2 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"All Roles\",\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: \"All Roles\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 361,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"admin\",\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: \"Admin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"cresent_user\",\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: \"Cresent User\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"airlines_user\",\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: \"Airlines User\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"btn-primary flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Settings_from_default_as_default_join_esm_icons_settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: \"Company Settings\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowCreateModal(true),\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"btn-primary flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_UserPlus_from_default_as_default_join_esm_icons_user_plus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: \"Create User\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: loadUsers,\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"btn-primary flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_RefreshCw_from_default_as_default_join_esm_icons_refresh_cw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: \"Refresh\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"card\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-4 text-gray-600\",\n                                                    children: \"Loading users...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"jsx-a858199ca2dfb051\" + \" \" + \"min-w-full divide-y divide-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"bg-gray-50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"jsx-a858199ca2dfb051\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"User\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"Role\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"Created\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"Last Login\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"Assigned Accounts\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"Actions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"bg-white divide-y divide-gray-200\",\n                                                        children: filteredUsers.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"jsx-a858199ca2dfb051\" + \" \" + \"hover:bg-gray-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-white text-sm font-medium\",\n                                                                                        children: user.full_name.charAt(0)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                        lineNumber: 418,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 417,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"ml-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                                                            children: user.full_name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                            lineNumber: 423,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-sm text-gray-500\",\n                                                                                            children: user.email\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                            lineNumber: 424,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 422,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 416,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getRoleColor(user.role)),\n                                                                            children: user.role.replace(\"_\", \" \")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 429,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getStatusColor(user.status)),\n                                                                            children: user.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 434,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                        lineNumber: 433,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                        children: new Date(user.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                        lineNumber: 438,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                        children: user.last_login ? new Date(user.last_login).toLocaleDateString() : \"Never\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                        children: user.assigned_accounts\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>editUser(user.id),\n                                                                                    title: \"Edit User\",\n                                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-blue-600 hover:text-blue-900\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Edit_from_default_as_default_join_esm_icons_pen_square_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                        lineNumber: 454,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 449,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>deleteUser(user.id),\n                                                                                    title: \"Delete User\",\n                                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-red-600 hover:text-red-900\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Trash2_from_default_as_default_join_esm_icons_trash_2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                        lineNumber: 461,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 456,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    title: \"More Options\",\n                                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-gray-600 hover:text-gray-900\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_MoreHorizontal_from_default_as_default_join_esm_icons_more_horizontal_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                        lineNumber: 464,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 463,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 448,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                        lineNumber: 447,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, user.id, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this),\n                                    showCreateModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"bg-white rounded-lg p-6 w-96\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-lg font-semibold mb-4\",\n                                                    children: \"Create New User\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: (e)=>{\n                                                        e.preventDefault();\n                                                        const formData = new FormData(e.target);\n                                                        createUser({\n                                                            username: formData.get(\"username\"),\n                                                            email: formData.get(\"email\"),\n                                                            full_name: formData.get(\"full_name\"),\n                                                            role: formData.get(\"role\"),\n                                                            status: \"active\"\n                                                        });\n                                                    },\n                                                    className: \"jsx-a858199ca2dfb051\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Username\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 494,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            name: \"username\",\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 495,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Email\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 498,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            name: \"email\",\n                                                                            type: \"email\",\n                                                                            required: true,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 499,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 497,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Full Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 502,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            name: \"full_name\",\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 503,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 501,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Role\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 506,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            name: \"role\",\n                                                                            required: true,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"cresent_user\",\n                                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                                    children: \"Cresent User\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 508,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"airlines_user\",\n                                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                                    children: \"Airlines User\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 509,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"admin\",\n                                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                                    children: \"Admin\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 510,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 507,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex justify-end space-x-3 mt-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setShowCreateModal(false),\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                                    children: \"Cancel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 515,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"submit\",\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"btn-primary\",\n                                                                    children: \"Create User\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 522,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 17\n                                    }, this),\n                                    showEditModal && editingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"bg-white rounded-lg p-6 w-96\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-lg font-semibold mb-4\",\n                                                    children: \"Edit User\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: (e)=>{\n                                                        e.preventDefault();\n                                                        const formData = new FormData(e.target);\n                                                        updateUser({\n                                                            username: formData.get(\"username\"),\n                                                            email: formData.get(\"email\"),\n                                                            full_name: formData.get(\"full_name\"),\n                                                            role: formData.get(\"role\"),\n                                                            status: formData.get(\"status\")\n                                                        });\n                                                    },\n                                                    className: \"jsx-a858199ca2dfb051\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Username\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 552,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            name: \"username\",\n                                                                            type: \"text\",\n                                                                            defaultValue: editingUser.username,\n                                                                            required: true,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 553,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 551,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Email\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 562,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            name: \"email\",\n                                                                            type: \"email\",\n                                                                            defaultValue: editingUser.email,\n                                                                            required: true,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 563,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Full Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 572,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            name: \"full_name\",\n                                                                            type: \"text\",\n                                                                            defaultValue: editingUser.full_name,\n                                                                            required: true,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 573,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Role\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 582,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            name: \"role\",\n                                                                            defaultValue: editingUser.role,\n                                                                            required: true,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"cresent_user\",\n                                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                                    children: \"Cresent User\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 589,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"airlines_user\",\n                                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                                    children: \"Airlines User\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 590,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"admin\",\n                                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                                    children: \"Admin\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 591,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 583,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Status\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 595,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            name: \"status\",\n                                                                            defaultValue: editingUser.status,\n                                                                            required: true,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"active\",\n                                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                                    children: \"Active\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 602,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"inactive\",\n                                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                                    children: \"Inactive\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 603,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 596,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 594,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex justify-end space-x-3 mt-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>{\n                                                                        setShowEditModal(false);\n                                                                        setEditingUser(null);\n                                                                    },\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                                    children: \"Cancel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 608,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"submit\",\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"btn-primary\",\n                                                                    children: \"Update User\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 618,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 607,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(UserManagement, \"px3xDZ3baxyZfdcN3d/17vaT00A=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = UserManagement;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.withAuth)(UserManagement));\nvar _c, _c1;\n$RefreshReg$(_c, \"UserManagement\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/admin/users.tsx\n"));

/***/ })

});