"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/users",{

/***/ "./src/pages/admin/users.tsx":
/*!***********************************!*\
  !*** ./src/pages/admin/users.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/Navigation */ \"./src/components/Navigation.tsx\");\n/* harmony import */ var modularize_import_loader_name_Users_from_default_as_default_join_esm_icons_users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! modularize-import-loader?name=Users&from=default&as=default&join=../esm/icons/users!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Users&from=default&as=default&join=../esm/icons/users!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_UserPlus_from_default_as_default_join_esm_icons_user_plus_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! modularize-import-loader?name=UserPlus&from=default&as=default&join=../esm/icons/user-plus!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=UserPlus&from=default&as=default&join=../esm/icons/user-plus!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Search_from_default_as_default_join_esm_icons_search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! modularize-import-loader?name=Search&from=default&as=default&join=../esm/icons/search!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Search&from=default&as=default&join=../esm/icons/search!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_MoreHorizontal_from_default_as_default_join_esm_icons_more_horizontal_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! modularize-import-loader?name=MoreHorizontal&from=default&as=default&join=../esm/icons/more-horizontal!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=MoreHorizontal&from=default&as=default&join=../esm/icons/more-horizontal!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Edit_from_default_as_default_join_esm_icons_pen_square_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! modularize-import-loader?name=Edit&from=default&as=default&join=../esm/icons/pen-square!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Edit&from=default&as=default&join=../esm/icons/pen-square!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Trash2_from_default_as_default_join_esm_icons_trash_2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! modularize-import-loader?name=Trash2&from=default&as=default&join=../esm/icons/trash-2!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Trash2&from=default&as=default&join=../esm/icons/trash-2!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Shield_from_default_as_default_join_esm_icons_shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! modularize-import-loader?name=Shield&from=default&as=default&join=../esm/icons/shield!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Shield&from=default&as=default&join=../esm/icons/shield!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Settings_from_default_as_default_join_esm_icons_settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! modularize-import-loader?name=Settings&from=default&as=default&join=../esm/icons/settings!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Settings&from=default&as=default&join=../esm/icons/settings!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_RefreshCw_from_default_as_default_join_esm_icons_refresh_cw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! modularize-import-loader?name=RefreshCw&from=default&as=default&join=../esm/icons/refresh-cw!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=RefreshCw&from=default&as=default&join=../esm/icons/refresh-cw!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction UserManagement() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [roleFilter, setRoleFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"All Roles\");\n    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [editingUser, setEditingUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const userStats = {\n        total: users.length,\n        admin: users.filter((u)=>u.role === \"admin\").length,\n        infra: users.filter((u)=>u.role === \"cresent_user\").length,\n        marketing: users.filter((u)=>u.role === \"airlines_user\").length\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        loadUsers();\n    }, []);\n    const loadUsers = async ()=>{\n        try {\n            setLoading(true);\n            // Try to fetch real users from API\n            try {\n                const response = await fetch(\"/api/v1/admin/users\", {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"session_token\"))\n                    }\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    setUsers(data.users || []);\n                    return;\n                }\n            } catch (e) {\n                console.log(\"Users API not available, using mock data\");\n            }\n            // Fallback to mock data\n            const mockUsers = [\n                {\n                    id: \"1\",\n                    username: \"admin\",\n                    email: \"<EMAIL>\",\n                    full_name: \"System Administrator\",\n                    role: \"admin\",\n                    status: \"active\",\n                    created_at: \"2024-01-01T00:00:00Z\",\n                    last_login: \"2024-01-08T10:30:00Z\",\n                    assigned_accounts: 5\n                },\n                {\n                    id: \"2\",\n                    username: \"cresent_reviewer\",\n                    email: \"<EMAIL>\",\n                    full_name: \"Cresent Reviewer\",\n                    role: \"cresent_user\",\n                    status: \"active\",\n                    created_at: \"2024-01-02T00:00:00Z\",\n                    last_login: \"2024-01-08T09:15:00Z\",\n                    assigned_accounts: 3\n                },\n                {\n                    id: \"3\",\n                    username: \"airline_approver\",\n                    email: \"<EMAIL>\",\n                    full_name: \"Airline Approver\",\n                    role: \"airlines_user\",\n                    status: \"active\",\n                    created_at: \"2024-01-03T00:00:00Z\",\n                    last_login: \"2024-01-07T16:45:00Z\",\n                    assigned_accounts: 2\n                }\n            ];\n            setUsers(mockUsers);\n        } catch (error) {\n            console.error(\"Failed to load users:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createUser = async (userData)=>{\n        try {\n            const response = await fetch(\"/api/v1/admin/users\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"session_token\"))\n                },\n                body: JSON.stringify(userData)\n            });\n            if (response.ok) {\n                alert(\"User created successfully!\");\n                loadUsers();\n                setShowCreateModal(false);\n            } else {\n                alert(\"Failed to create user\");\n            }\n        } catch (error) {\n            console.error(\"Error creating user:\", error);\n            alert(\"Error creating user\");\n        }\n    };\n    const deleteUser = async (userId)=>{\n        if (!confirm(\"Are you sure you want to delete this user?\")) return;\n        try {\n            const response = await fetch(\"/api/v1/admin/users/\".concat(userId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"session_token\"))\n                }\n            });\n            if (response.ok) {\n                alert(\"User deleted successfully!\");\n                loadUsers();\n            } else {\n                alert(\"Failed to delete user\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting user:\", error);\n            alert(\"Error deleting user\");\n        }\n    };\n    const editUser = async (userId)=>{\n        // Find the user to edit\n        const user = users.find((u)=>u.id === userId);\n        if (!user) return;\n        setEditingUser(user);\n        setShowEditModal(true);\n    };\n    const updateUser = async (userData)=>{\n        if (!editingUser) return;\n        try {\n            const response = await fetch(\"/api/v1/admin/users/\".concat(editingUser.id), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"session_token\"))\n                },\n                body: JSON.stringify(userData)\n            });\n            if (response.ok) {\n                alert(\"User updated successfully!\");\n                setShowEditModal(false);\n                setEditingUser(null);\n                loadUsers();\n            } else {\n                alert(\"Failed to update user\");\n            }\n        } catch (error) {\n            console.error(\"Error updating user:\", error);\n            alert(\"Error updating user\");\n        }\n    };\n    const filteredUsers = users.filter((user)=>{\n        const matchesSearch = user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) || user.username.toLowerCase().includes(searchTerm.toLowerCase()) || user.email.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesRole = roleFilter === \"All Roles\" || user.role === roleFilter;\n        return matchesSearch && matchesRole;\n    });\n    const getRoleColor = (role)=>{\n        switch(role){\n            case \"admin\":\n                return \"bg-purple-100 text-purple-800\";\n            case \"cresent_user\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"airlines_user\":\n                return \"bg-green-100 text-green-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"active\":\n                return \"bg-green-100 text-green-800\";\n            case \"inactive\":\n                return \"bg-red-100 text-red-800\";\n            case \"pending\":\n                return \"bg-yellow-100 text-yellow-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        className: \"jsx-a858199ca2dfb051\",\n                        children: \"User Management - Agentic ORM\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Manage users and their access permissions\",\n                        className: \"jsx-a858199ca2dfb051\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"a858199ca2dfb051\",\n                children: \".card.jsx-a858199ca2dfb051{background:white;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;-webkit-box-shadow:0 1px 3px 0 rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px 0 rgba(0,0,0,.1);box-shadow:0 1px 3px 0 rgba(0,0,0,.1);border:1px solid#e5e7eb;padding:1.5rem}.btn-primary.jsx-a858199ca2dfb051{background:#2563eb;color:white;font-weight:500;padding:.5rem 1rem;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;-webkit-transition:all.2s;-moz-transition:all.2s;-o-transition:all.2s;transition:all.2s;border:none;cursor:pointer}.btn-primary.jsx-a858199ca2dfb051:hover{background:#1d4ed8}.app-container.jsx-a858199ca2dfb051{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;min-height:100vh}.app-body.jsx-a858199ca2dfb051{margin-top:5rem;min-height:-webkit-calc(100vh - 5rem);min-height:-moz-calc(100vh - 5rem);min-height:calc(100vh - 5rem)}.main-content.jsx-a858199ca2dfb051{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;background:#f9fafb;width:100%}.stat-card.jsx-a858199ca2dfb051{background:white;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;padding:1.5rem;border:1px solid#e5e7eb;-webkit-box-shadow:0 1px 3px 0 rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px 0 rgba(0,0,0,.1);box-shadow:0 1px 3px 0 rgba(0,0,0,.1)}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-a858199ca2dfb051\" + \" \" + \"app-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        currentPage: \"admin\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"app-body\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"main-content\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-a858199ca2dfb051\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-2xl font-bold text-gray-900 mb-2\",\n                                                children: \"User Management\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-gray-600\",\n                                                children: \"Manage users and their access permissions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-a858199ca2dfb051\" + \" \" + \"stat-card\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"p-2 bg-blue-100 rounded-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Users_from_default_as_default_join_esm_icons_users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-5 h-5 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-sm text-gray-500\",\n                                                                    children: \"Total Users\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-2xl font-bold\",\n                                                                    children: userStats.total\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-a858199ca2dfb051\" + \" \" + \"stat-card\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"p-2 bg-green-100 rounded-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Shield_from_default_as_default_join_esm_icons_shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"w-5 h-5 text-green-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-sm text-gray-500\",\n                                                                    children: \"Admin Users\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 308,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-2xl font-bold\",\n                                                                    children: userStats.admin\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-a858199ca2dfb051\" + \" \" + \"stat-card\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"p-2 bg-purple-100 rounded-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Users_from_default_as_default_join_esm_icons_users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-5 h-5 text-purple-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-sm text-gray-500\",\n                                                                    children: \"Infra Users\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-2xl font-bold\",\n                                                                    children: userStats.infra\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-a858199ca2dfb051\" + \" \" + \"stat-card\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"p-2 bg-orange-100 rounded-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Users_from_default_as_default_join_esm_icons_users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-5 h-5 text-orange-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-sm text-gray-500\",\n                                                                    children: \"Marketing Users\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-2xl font-bold\",\n                                                                    children: userStats.marketing\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"card mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-lg font-semibold text-gray-900\",\n                                                    children: \"All Users\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex flex-col sm:flex-row gap-4 w-full sm:w-auto\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Search_from_default_as_default_join_esm_icons_search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    placeholder: \"Search users...\",\n                                                                    value: searchTerm,\n                                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"border border-gray-300 rounded-lg px-3 py-2 text-sm w-64\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: roleFilter,\n                                                            onChange: (e)=>setRoleFilter(e.target.value),\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"border border-gray-300 rounded-lg px-3 py-2 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"All Roles\",\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: \"All Roles\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 361,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"admin\",\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: \"Admin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"cresent_user\",\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: \"Cresent User\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"airlines_user\",\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: \"Airlines User\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"btn-primary flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Settings_from_default_as_default_join_esm_icons_settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: \"Company Settings\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowCreateModal(true),\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"btn-primary flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_UserPlus_from_default_as_default_join_esm_icons_user_plus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: \"Create User\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: loadUsers,\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"btn-primary flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_RefreshCw_from_default_as_default_join_esm_icons_refresh_cw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: \"Refresh\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"card\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-4 text-gray-600\",\n                                                    children: \"Loading users...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"jsx-a858199ca2dfb051\" + \" \" + \"min-w-full divide-y divide-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"bg-gray-50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"jsx-a858199ca2dfb051\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"User\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"Role\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"Created\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"Last Login\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"Assigned Accounts\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                    children: \"Actions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"bg-white divide-y divide-gray-200\",\n                                                        children: filteredUsers.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"jsx-a858199ca2dfb051\" + \" \" + \"hover:bg-gray-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-white text-sm font-medium\",\n                                                                                        children: user.full_name.charAt(0)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                        lineNumber: 418,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 417,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"ml-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                                                            children: user.full_name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                            lineNumber: 423,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-sm text-gray-500\",\n                                                                                            children: user.email\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                            lineNumber: 424,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 422,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 416,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getRoleColor(user.role)),\n                                                                            children: user.role.replace(\"_\", \" \")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 429,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getStatusColor(user.status)),\n                                                                            children: user.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 434,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                        lineNumber: 433,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                        children: new Date(user.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                        lineNumber: 438,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                        children: user.last_login ? new Date(user.last_login).toLocaleDateString() : \"Never\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                        children: user.assigned_accounts\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>editUser(user.id),\n                                                                                    title: \"Edit User\",\n                                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-blue-600 hover:text-blue-900\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Edit_from_default_as_default_join_esm_icons_pen_square_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                        lineNumber: 454,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 449,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>deleteUser(user.id),\n                                                                                    title: \"Delete User\",\n                                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-red-600 hover:text-red-900\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Trash2_from_default_as_default_join_esm_icons_trash_2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                        lineNumber: 461,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 456,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    title: \"More Options\",\n                                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-gray-600 hover:text-gray-900\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_MoreHorizontal_from_default_as_default_join_esm_icons_more_horizontal_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                        lineNumber: 464,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 463,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 448,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                        lineNumber: 447,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, user.id, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this),\n                                    showCreateModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"bg-white rounded-lg p-6 w-96\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-lg font-semibold mb-4\",\n                                                    children: \"Create New User\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: (e)=>{\n                                                        e.preventDefault();\n                                                        const formData = new FormData(e.target);\n                                                        createUser({\n                                                            username: formData.get(\"username\"),\n                                                            email: formData.get(\"email\"),\n                                                            full_name: formData.get(\"full_name\"),\n                                                            password: formData.get(\"password\"),\n                                                            role: formData.get(\"role\"),\n                                                            status: \"active\"\n                                                        });\n                                                    },\n                                                    className: \"jsx-a858199ca2dfb051\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Username\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 495,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            name: \"username\",\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 496,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Email\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 499,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            name: \"email\",\n                                                                            type: \"email\",\n                                                                            required: true,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 500,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 498,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Full Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 503,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            name: \"full_name\",\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 504,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Password\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 507,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            name: \"password\",\n                                                                            type: \"password\",\n                                                                            required: true,\n                                                                            minLength: 8,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 508,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-xs text-gray-500 mt-1\",\n                                                                            children: \"Minimum 8 characters\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 509,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Role\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 512,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            name: \"role\",\n                                                                            required: true,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"cresent_user\",\n                                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                                    children: \"Cresent User\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 514,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"airlines_user\",\n                                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                                    children: \"Airlines User\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 515,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"admin\",\n                                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                                    children: \"Admin\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 516,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 513,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex justify-end space-x-3 mt-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setShowCreateModal(false),\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                                    children: \"Cancel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"submit\",\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"btn-primary\",\n                                                                    children: \"Create User\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 528,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 17\n                                    }, this),\n                                    showEditModal && editingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a858199ca2dfb051\" + \" \" + \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"bg-white rounded-lg p-6 w-96\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"text-lg font-semibold mb-4\",\n                                                    children: \"Edit User\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: (e)=>{\n                                                        e.preventDefault();\n                                                        const formData = new FormData(e.target);\n                                                        updateUser({\n                                                            username: formData.get(\"username\"),\n                                                            email: formData.get(\"email\"),\n                                                            full_name: formData.get(\"full_name\"),\n                                                            role: formData.get(\"role\"),\n                                                            status: formData.get(\"status\")\n                                                        });\n                                                    },\n                                                    className: \"jsx-a858199ca2dfb051\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Username\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 558,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            name: \"username\",\n                                                                            type: \"text\",\n                                                                            defaultValue: editingUser.username,\n                                                                            required: true,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Email\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 568,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            name: \"email\",\n                                                                            type: \"email\",\n                                                                            defaultValue: editingUser.email,\n                                                                            required: true,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Full Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 578,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            name: \"full_name\",\n                                                                            type: \"text\",\n                                                                            defaultValue: editingUser.full_name,\n                                                                            required: true,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 579,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 577,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Role\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 588,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            name: \"role\",\n                                                                            defaultValue: editingUser.role,\n                                                                            required: true,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"cresent_user\",\n                                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                                    children: \"Cresent User\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 595,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"airlines_user\",\n                                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                                    children: \"Airlines User\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 596,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"admin\",\n                                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                                    children: \"Admin\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 597,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 589,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 587,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"block text-sm font-medium text-gray-700\",\n                                                                            children: \"Status\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 601,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            name: \"status\",\n                                                                            defaultValue: editingUser.status,\n                                                                            required: true,\n                                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"active\",\n                                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                                    children: \"Active\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 608,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"inactive\",\n                                                                                    className: \"jsx-a858199ca2dfb051\",\n                                                                                    children: \"Inactive\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                                    lineNumber: 609,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                            lineNumber: 602,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 600,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a858199ca2dfb051\" + \" \" + \"flex justify-end space-x-3 mt-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>{\n                                                                        setShowEditModal(false);\n                                                                        setEditingUser(null);\n                                                                    },\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                                    children: \"Cancel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 614,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"submit\",\n                                                                    className: \"jsx-a858199ca2dfb051\" + \" \" + \"btn-primary\",\n                                                                    children: \"Update User\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                                    lineNumber: 624,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/admin/users.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(UserManagement, \"px3xDZ3baxyZfdcN3d/17vaT00A=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = UserManagement;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.withAuth)(UserManagement));\nvar _c, _c1;\n$RefreshReg$(_c, \"UserManagement\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvYWRtaW4vdXNlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTJDO0FBQ2Y7QUFDa0M7QUFDVjtBQVkvQjtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFjckIsU0FBU2U7O0lBQ1AsTUFBTSxFQUFFQyxJQUFJLEVBQUUsR0FBR1osOERBQU9BO0lBQ3hCLE1BQU0sQ0FBQ2EsT0FBT0MsU0FBUyxHQUFHbEIsK0NBQVFBLENBQVMsRUFBRTtJQUM3QyxNQUFNLENBQUNtQixTQUFTQyxXQUFXLEdBQUdwQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNxQixZQUFZQyxjQUFjLEdBQUd0QiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUN1QixZQUFZQyxjQUFjLEdBQUd4QiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUN5QixpQkFBaUJDLG1CQUFtQixHQUFHMUIsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDMkIsZUFBZUMsaUJBQWlCLEdBQUc1QiwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUM2QixhQUFhQyxlQUFlLEdBQUc5QiwrQ0FBUUEsQ0FBYztJQUU1RCxNQUFNK0IsWUFBWTtRQUNoQkMsT0FBT2YsTUFBTWdCLE1BQU07UUFDbkJDLE9BQU9qQixNQUFNa0IsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxJQUFJLEtBQUssU0FBU0osTUFBTTtRQUNuREssT0FBT3JCLE1BQU1rQixNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLElBQUksS0FBSyxnQkFBZ0JKLE1BQU07UUFDMURNLFdBQVd0QixNQUFNa0IsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxJQUFJLEtBQUssaUJBQWlCSixNQUFNO0lBQ2pFO0lBRUFoQyxnREFBU0EsQ0FBQztRQUNSdUM7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNQSxZQUFZO1FBQ2hCLElBQUk7WUFDRnBCLFdBQVc7WUFFWCxtQ0FBbUM7WUFDbkMsSUFBSTtnQkFDRixNQUFNcUIsV0FBVyxNQUFNQyxNQUFNLHVCQUF1QjtvQkFDbERDLFNBQVM7d0JBQ1AsaUJBQWlCLFVBQWdELE9BQXRDQyxhQUFhQyxPQUFPLENBQUM7b0JBQ2xEO2dCQUNGO2dCQUVBLElBQUlKLFNBQVNLLEVBQUUsRUFBRTtvQkFDZixNQUFNQyxPQUFPLE1BQU1OLFNBQVNPLElBQUk7b0JBQ2hDOUIsU0FBUzZCLEtBQUs5QixLQUFLLElBQUksRUFBRTtvQkFDekI7Z0JBQ0Y7WUFDRixFQUFFLE9BQU9nQyxHQUFHO2dCQUNWQyxRQUFRQyxHQUFHLENBQUM7WUFDZDtZQUVBLHdCQUF3QjtZQUN4QixNQUFNQyxZQUFvQjtnQkFDeEI7b0JBQ0VDLElBQUk7b0JBQ0pDLFVBQVU7b0JBQ1ZDLE9BQU87b0JBQ1BDLFdBQVc7b0JBQ1huQixNQUFNO29CQUNOb0IsUUFBUTtvQkFDUkMsWUFBWTtvQkFDWkMsWUFBWTtvQkFDWkMsbUJBQW1CO2dCQUNyQjtnQkFDQTtvQkFDRVAsSUFBSTtvQkFDSkMsVUFBVTtvQkFDVkMsT0FBTztvQkFDUEMsV0FBVztvQkFDWG5CLE1BQU07b0JBQ05vQixRQUFRO29CQUNSQyxZQUFZO29CQUNaQyxZQUFZO29CQUNaQyxtQkFBbUI7Z0JBQ3JCO2dCQUNBO29CQUNFUCxJQUFJO29CQUNKQyxVQUFVO29CQUNWQyxPQUFPO29CQUNQQyxXQUFXO29CQUNYbkIsTUFBTTtvQkFDTm9CLFFBQVE7b0JBQ1JDLFlBQVk7b0JBQ1pDLFlBQVk7b0JBQ1pDLG1CQUFtQjtnQkFDckI7YUFDRDtZQUNEMUMsU0FBU2tDO1FBQ1gsRUFBRSxPQUFPUyxPQUFPO1lBQ2RYLFFBQVFXLEtBQUssQ0FBQyx5QkFBeUJBO1FBQ3pDLFNBQVU7WUFDUnpDLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTTBDLGFBQWEsT0FBT0M7UUFDeEIsSUFBSTtZQUNGLE1BQU10QixXQUFXLE1BQU1DLE1BQU0sdUJBQXVCO2dCQUNsRHNCLFFBQVE7Z0JBQ1JyQixTQUFTO29CQUNQLGdCQUFnQjtvQkFDaEIsaUJBQWlCLFVBQWdELE9BQXRDQyxhQUFhQyxPQUFPLENBQUM7Z0JBQ2xEO2dCQUNBb0IsTUFBTUMsS0FBS0MsU0FBUyxDQUFDSjtZQUN2QjtZQUVBLElBQUl0QixTQUFTSyxFQUFFLEVBQUU7Z0JBQ2ZzQixNQUFNO2dCQUNONUI7Z0JBQ0FkLG1CQUFtQjtZQUNyQixPQUFPO2dCQUNMMEMsTUFBTTtZQUNSO1FBQ0YsRUFBRSxPQUFPUCxPQUFPO1lBQ2RYLFFBQVFXLEtBQUssQ0FBQyx3QkFBd0JBO1lBQ3RDTyxNQUFNO1FBQ1I7SUFDRjtJQUVBLE1BQU1DLGFBQWEsT0FBT0M7UUFDeEIsSUFBSSxDQUFDQyxRQUFRLCtDQUErQztRQUU1RCxJQUFJO1lBQ0YsTUFBTTlCLFdBQVcsTUFBTUMsTUFBTSx1QkFBOEIsT0FBUDRCLFNBQVU7Z0JBQzVETixRQUFRO2dCQUNSckIsU0FBUztvQkFDUCxpQkFBaUIsVUFBZ0QsT0FBdENDLGFBQWFDLE9BQU8sQ0FBQztnQkFDbEQ7WUFDRjtZQUVBLElBQUlKLFNBQVNLLEVBQUUsRUFBRTtnQkFDZnNCLE1BQU07Z0JBQ041QjtZQUNGLE9BQU87Z0JBQ0w0QixNQUFNO1lBQ1I7UUFDRixFQUFFLE9BQU9QLE9BQU87WUFDZFgsUUFBUVcsS0FBSyxDQUFDLHdCQUF3QkE7WUFDdENPLE1BQU07UUFDUjtJQUNGO0lBRUEsTUFBTUksV0FBVyxPQUFPRjtRQUN0Qix3QkFBd0I7UUFDeEIsTUFBTXRELE9BQU9DLE1BQU13RCxJQUFJLENBQUNyQyxDQUFBQSxJQUFLQSxFQUFFaUIsRUFBRSxLQUFLaUI7UUFDdEMsSUFBSSxDQUFDdEQsTUFBTTtRQUVYYyxlQUFlZDtRQUNmWSxpQkFBaUI7SUFDbkI7SUFFQSxNQUFNOEMsYUFBYSxPQUFPWDtRQUN4QixJQUFJLENBQUNsQyxhQUFhO1FBRWxCLElBQUk7WUFDRixNQUFNWSxXQUFXLE1BQU1DLE1BQU0sdUJBQXNDLE9BQWZiLFlBQVl3QixFQUFFLEdBQUk7Z0JBQ3BFVyxRQUFRO2dCQUNSckIsU0FBUztvQkFDUCxnQkFBZ0I7b0JBQ2hCLGlCQUFpQixVQUFnRCxPQUF0Q0MsYUFBYUMsT0FBTyxDQUFDO2dCQUNsRDtnQkFDQW9CLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ0o7WUFDdkI7WUFFQSxJQUFJdEIsU0FBU0ssRUFBRSxFQUFFO2dCQUNmc0IsTUFBTTtnQkFDTnhDLGlCQUFpQjtnQkFDakJFLGVBQWU7Z0JBQ2ZVO1lBQ0YsT0FBTztnQkFDTDRCLE1BQU07WUFDUjtRQUNGLEVBQUUsT0FBT1AsT0FBTztZQUNkWCxRQUFRVyxLQUFLLENBQUMsd0JBQXdCQTtZQUN0Q08sTUFBTTtRQUNSO0lBQ0Y7SUFFQSxNQUFNTyxnQkFBZ0IxRCxNQUFNa0IsTUFBTSxDQUFDbkIsQ0FBQUE7UUFDakMsTUFBTTRELGdCQUFnQjVELEtBQUt3QyxTQUFTLENBQUNxQixXQUFXLEdBQUdDLFFBQVEsQ0FBQ3pELFdBQVd3RCxXQUFXLE9BQzdEN0QsS0FBS3NDLFFBQVEsQ0FBQ3VCLFdBQVcsR0FBR0MsUUFBUSxDQUFDekQsV0FBV3dELFdBQVcsT0FDM0Q3RCxLQUFLdUMsS0FBSyxDQUFDc0IsV0FBVyxHQUFHQyxRQUFRLENBQUN6RCxXQUFXd0QsV0FBVztRQUM3RSxNQUFNRSxjQUFjeEQsZUFBZSxlQUFlUCxLQUFLcUIsSUFBSSxLQUFLZDtRQUNoRSxPQUFPcUQsaUJBQWlCRztJQUMxQjtJQUVBLE1BQU1DLGVBQWUsQ0FBQzNDO1FBQ3BCLE9BQVFBO1lBQ04sS0FBSztnQkFBUyxPQUFPO1lBQ3JCLEtBQUs7Z0JBQWdCLE9BQU87WUFDNUIsS0FBSztnQkFBaUIsT0FBTztZQUM3QjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNNEMsaUJBQWlCLENBQUN4QjtRQUN0QixPQUFRQTtZQUNOLEtBQUs7Z0JBQVUsT0FBTztZQUN0QixLQUFLO2dCQUFZLE9BQU87WUFDeEIsS0FBSztnQkFBVyxPQUFPO1lBQ3ZCO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLHFCQUNFOzswQkFDRSw4REFBQ3ZELGtEQUFJQTs7a0NBQ0gsOERBQUNnRjs7a0NBQU07Ozs7OztrQ0FDUCw4REFBQ0M7d0JBQUtDLE1BQUs7d0JBQWNDLFNBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQStDbkMsOERBQUNDOzBEQUFjOztrQ0FDYiw4REFBQ2pGLDhEQUFVQTt3QkFBQ2tGLGFBQVk7Ozs7OztrQ0FFeEIsOERBQUNEO2tFQUFjO2tDQUNiLDRFQUFDRTtzRUFBZTtzQ0FDZCw0RUFBQ0Y7MEVBQWM7O2tEQUViLDhEQUFDQTtrRkFBYzs7MERBQ2IsOERBQUNHOzBGQUFhOzBEQUF3Qzs7Ozs7OzBEQUN0RCw4REFBQ0M7MEZBQVk7MERBQWdCOzs7Ozs7Ozs7Ozs7a0RBSS9CLDhEQUFDSjtrRkFBYzs7MERBQ2IsOERBQUNBOzBGQUFjOzBEQUNiLDRFQUFDQTs4RkFBYzs7c0VBQ2IsOERBQUNBO3NHQUFjO3NFQUNiLDRFQUFDaEYscUlBQUtBO2dFQUFDcUYsV0FBVTs7Ozs7Ozs7Ozs7c0VBRW5CLDhEQUFDTDs7OzhFQUNDLDhEQUFDSTs4R0FBWTs4RUFBd0I7Ozs7Ozs4RUFDckMsOERBQUNBOzhHQUFZOzhFQUFzQjNELFVBQVVDLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUt4RCw4REFBQ3NEOzBGQUFjOzBEQUNiLDRFQUFDQTs4RkFBYzs7c0VBQ2IsOERBQUNBO3NHQUFjO3NFQUNiLDRFQUFDMUUsdUlBQU1BO2dFQUFDK0UsV0FBVTs7Ozs7Ozs7Ozs7c0VBRXBCLDhEQUFDTDs7OzhFQUNDLDhEQUFDSTs4R0FBWTs4RUFBd0I7Ozs7Ozs4RUFDckMsOERBQUNBOzhHQUFZOzhFQUFzQjNELFVBQVVHLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUt4RCw4REFBQ29EOzBGQUFjOzBEQUNiLDRFQUFDQTs4RkFBYzs7c0VBQ2IsOERBQUNBO3NHQUFjO3NFQUNiLDRFQUFDaEYscUlBQUtBO2dFQUFDcUYsV0FBVTs7Ozs7Ozs7Ozs7c0VBRW5CLDhEQUFDTDs7OzhFQUNDLDhEQUFDSTs4R0FBWTs4RUFBd0I7Ozs7Ozs4RUFDckMsOERBQUNBOzhHQUFZOzhFQUFzQjNELFVBQVVPLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUt4RCw4REFBQ2dEOzBGQUFjOzBEQUNiLDRFQUFDQTs4RkFBYzs7c0VBQ2IsOERBQUNBO3NHQUFjO3NFQUNiLDRFQUFDaEYscUlBQUtBO2dFQUFDcUYsV0FBVTs7Ozs7Ozs7Ozs7c0VBRW5CLDhEQUFDTDs7OzhFQUNDLDhEQUFDSTs4R0FBWTs4RUFBd0I7Ozs7Ozs4RUFDckMsOERBQUNBOzhHQUFZOzhFQUFzQjNELFVBQVVRLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQU85RCw4REFBQytDO2tGQUFjO2tEQUNiLDRFQUFDQTtzRkFBYzs7OERBQ2IsOERBQUNNOzhGQUFhOzhEQUFzQzs7Ozs7OzhEQUVwRCw4REFBQ047OEZBQWM7O3NFQUNiLDhEQUFDQTtzR0FBYzs7OEVBQ2IsOERBQUM5RSx1SUFBTUE7b0VBQUNtRixXQUFVOzs7Ozs7OEVBQ2xCLDhEQUFDRTtvRUFDQ0MsTUFBSztvRUFDTEMsYUFBWTtvRUFDWkMsT0FBTzNFO29FQUNQNEUsVUFBVSxDQUFDaEQsSUFBTTNCLGNBQWMyQixFQUFFaUQsTUFBTSxDQUFDRixLQUFLOzhHQUNuQzs7Ozs7Ozs7Ozs7O3NFQUlkLDhEQUFDRzs0REFDQ0gsT0FBT3pFOzREQUNQMEUsVUFBVSxDQUFDaEQsSUFBTXpCLGNBQWN5QixFQUFFaUQsTUFBTSxDQUFDRixLQUFLO3NHQUNuQzs7OEVBRVYsOERBQUNJO29FQUFPSixPQUFNOzs4RUFBWTs7Ozs7OzhFQUMxQiw4REFBQ0k7b0VBQU9KLE9BQU07OzhFQUFROzs7Ozs7OEVBQ3RCLDhEQUFDSTtvRUFBT0osT0FBTTs7OEVBQWU7Ozs7Ozs4RUFDN0IsOERBQUNJO29FQUFPSixPQUFNOzs4RUFBZ0I7Ozs7Ozs7Ozs7OztzRUFHaEMsOERBQUNLO3NHQUFpQjs7OEVBQ2hCLDhEQUFDeEYsMklBQVFBO29FQUFDOEUsV0FBVTs7Ozs7OzhFQUNwQiw4REFBQ1c7OzhFQUFLOzs7Ozs7Ozs7Ozs7c0VBR1IsOERBQUNEOzREQUNDRSxTQUFTLElBQU03RSxtQkFBbUI7c0dBQ3hCOzs4RUFFViw4REFBQ25CLDZJQUFRQTtvRUFBQ29GLFdBQVU7Ozs7Ozs4RUFDcEIsOERBQUNXOzs4RUFBSzs7Ozs7Ozs7Ozs7O3NFQUdSLDhEQUFDRDs0REFDQ0UsU0FBUy9EO3NHQUNDOzs4RUFFViw4REFBQzFCLCtJQUFTQTtvRUFBQzZFLFdBQVU7Ozs7Ozs4RUFDckIsOERBQUNXOzs4RUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBT2QsOERBQUNoQjtrRkFBYztrREFDWm5FLHdCQUNDLDhEQUFDbUU7c0ZBQWM7OzhEQUNiLDhEQUFDQTs4RkFBYzs7Ozs7OzhEQUNmLDhEQUFDSTs4RkFBWTs4REFBcUI7Ozs7Ozs7Ozs7O2lFQUdwQyw4REFBQ0o7c0ZBQWM7c0RBQ2IsNEVBQUNrQjswRkFBZ0I7O2tFQUNmLDhEQUFDQztrR0FBZ0I7a0VBQ2YsNEVBQUNDOzs7OEVBQ0MsOERBQUNDOzhHQUFhOzhFQUFpRjs7Ozs7OzhFQUMvRiw4REFBQ0E7OEdBQWE7OEVBQWlGOzs7Ozs7OEVBQy9GLDhEQUFDQTs4R0FBYTs4RUFBaUY7Ozs7Ozs4RUFDL0YsOERBQUNBOzhHQUFhOzhFQUFpRjs7Ozs7OzhFQUMvRiw4REFBQ0E7OEdBQWE7OEVBQWlGOzs7Ozs7OEVBQy9GLDhEQUFDQTs4R0FBYTs4RUFBaUY7Ozs7Ozs4RUFDL0YsOERBQUNBOzhHQUFhOzhFQUFpRjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBR25HLDhEQUFDQztrR0FBZ0I7a0VBQ2RqQyxjQUFja0MsR0FBRyxDQUFDLENBQUM3RixxQkFDbEIsOERBQUMwRjswR0FBMkI7O2tGQUMxQiw4REFBQ0k7a0hBQWE7a0ZBQ1osNEVBQUN4QjtzSEFBYzs7OEZBQ2IsOERBQUNBOzhIQUFjOzhGQUNiLDRFQUFDZ0I7a0lBQWU7a0dBQ2J0RixLQUFLd0MsU0FBUyxDQUFDdUQsTUFBTSxDQUFDOzs7Ozs7Ozs7Ozs4RkFHM0IsOERBQUN6Qjs4SEFBYzs7c0dBQ2IsOERBQUNBO3NJQUFjO3NHQUFxQ3RFLEtBQUt3QyxTQUFTOzs7Ozs7c0dBQ2xFLDhEQUFDOEI7c0lBQWM7c0dBQXlCdEUsS0FBS3VDLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tGQUl4RCw4REFBQ3VEO2tIQUFhO2tGQUNaLDRFQUFDUjtzSEFBZ0IsMkVBQW1HLE9BQXhCdEIsYUFBYWhFLEtBQUtxQixJQUFJO3NGQUMvR3JCLEtBQUtxQixJQUFJLENBQUMyRSxPQUFPLENBQUMsS0FBSzs7Ozs7Ozs7Ozs7a0ZBRzVCLDhEQUFDRjtrSEFBYTtrRkFDWiw0RUFBQ1I7c0hBQWdCLDJFQUF1RyxPQUE1QnJCLGVBQWVqRSxLQUFLeUMsTUFBTTtzRkFDbkh6QyxLQUFLeUMsTUFBTTs7Ozs7Ozs7Ozs7a0ZBR2hCLDhEQUFDcUQ7a0hBQWE7a0ZBQ1gsSUFBSUcsS0FBS2pHLEtBQUswQyxVQUFVLEVBQUV3RCxrQkFBa0I7Ozs7OztrRkFFL0MsOERBQUNKO2tIQUFhO2tGQUNYOUYsS0FBSzJDLFVBQVUsR0FBRyxJQUFJc0QsS0FBS2pHLEtBQUsyQyxVQUFVLEVBQUV1RCxrQkFBa0IsS0FBSzs7Ozs7O2tGQUV0RSw4REFBQ0o7a0hBQWE7a0ZBQ1g5RixLQUFLNEMsaUJBQWlCOzs7Ozs7a0ZBRXpCLDhEQUFDa0Q7a0hBQWE7a0ZBQ1osNEVBQUN4QjtzSEFBYzs7OEZBQ2IsOERBQUNlO29GQUNDRSxTQUFTLElBQU0vQixTQUFTeEQsS0FBS3FDLEVBQUU7b0ZBRS9CNkIsT0FBTTs4SEFESTs4RkFHViw0RUFBQ3hFLDBJQUFJQTt3RkFBQ2lGLFdBQVU7Ozs7Ozs7Ozs7OzhGQUVsQiw4REFBQ1U7b0ZBQ0NFLFNBQVMsSUFBTWxDLFdBQVdyRCxLQUFLcUMsRUFBRTtvRkFFakM2QixPQUFNOzhIQURJOzhGQUdWLDRFQUFDdkUseUlBQU1BO3dGQUFDZ0YsV0FBVTs7Ozs7Ozs7Ozs7OEZBRXBCLDhEQUFDVTtvRkFBcURuQixPQUFNOzhIQUExQzs4RkFDaEIsNEVBQUN6RSx5SkFBY0E7d0ZBQUNrRixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsrREFsRHpCM0UsS0FBS3FDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29DQStEM0I1QixpQ0FDQyw4REFBQzZEO2tGQUFjO2tEQUNiLDRFQUFDQTtzRkFBYzs7OERBQ2IsOERBQUNNOzhGQUFhOzhEQUE2Qjs7Ozs7OzhEQUMzQyw4REFBQ3VCO29EQUFLQyxVQUFVLENBQUNuRTt3REFDZkEsRUFBRW9FLGNBQWM7d0RBQ2hCLE1BQU1DLFdBQVcsSUFBSUMsU0FBU3RFLEVBQUVpRCxNQUFNO3dEQUN0Q3BDLFdBQVc7NERBQ1RSLFVBQVVnRSxTQUFTRSxHQUFHLENBQUM7NERBQ3ZCakUsT0FBTytELFNBQVNFLEdBQUcsQ0FBQzs0REFDcEJoRSxXQUFXOEQsU0FBU0UsR0FBRyxDQUFDOzREQUN4QkMsVUFBVUgsU0FBU0UsR0FBRyxDQUFDOzREQUN2Qm5GLE1BQU1pRixTQUFTRSxHQUFHLENBQUM7NERBQ25CL0QsUUFBUTt3REFDVjtvREFDRjs7O3NFQUNFLDhEQUFDNkI7c0dBQWM7OzhFQUNiLDhEQUFDQTs7O3NGQUNDLDhEQUFDb0M7c0hBQWdCO3NGQUEwQzs7Ozs7O3NGQUMzRCw4REFBQzdCOzRFQUFNVCxNQUFLOzRFQUFXVSxNQUFLOzRFQUFPNkIsUUFBUTtzSEFBVzs7Ozs7Ozs7Ozs7OzhFQUV4RCw4REFBQ3JDOzs7c0ZBQ0MsOERBQUNvQztzSEFBZ0I7c0ZBQTBDOzs7Ozs7c0ZBQzNELDhEQUFDN0I7NEVBQU1ULE1BQUs7NEVBQVFVLE1BQUs7NEVBQVE2QixRQUFRO3NIQUFXOzs7Ozs7Ozs7Ozs7OEVBRXRELDhEQUFDckM7OztzRkFDQyw4REFBQ29DO3NIQUFnQjtzRkFBMEM7Ozs7OztzRkFDM0QsOERBQUM3Qjs0RUFBTVQsTUFBSzs0RUFBWVUsTUFBSzs0RUFBTzZCLFFBQVE7c0hBQVc7Ozs7Ozs7Ozs7Ozs4RUFFekQsOERBQUNyQzs7O3NGQUNDLDhEQUFDb0M7c0hBQWdCO3NGQUEwQzs7Ozs7O3NGQUMzRCw4REFBQzdCOzRFQUFNVCxNQUFLOzRFQUFXVSxNQUFLOzRFQUFXNkIsUUFBUTs0RUFBQ0MsV0FBVztzSEFBYTs7Ozs7O3NGQUN4RSw4REFBQ2xDO3NIQUFZO3NGQUE2Qjs7Ozs7Ozs7Ozs7OzhFQUU1Qyw4REFBQ0o7OztzRkFDQyw4REFBQ29DO3NIQUFnQjtzRkFBMEM7Ozs7OztzRkFDM0QsOERBQUN2Qjs0RUFBT2YsTUFBSzs0RUFBT3VDLFFBQVE7c0hBQVc7OzhGQUNyQyw4REFBQ3ZCO29GQUFPSixPQUFNOzs4RkFBZTs7Ozs7OzhGQUM3Qiw4REFBQ0k7b0ZBQU9KLE9BQU07OzhGQUFnQjs7Ozs7OzhGQUM5Qiw4REFBQ0k7b0ZBQU9KLE9BQU07OzhGQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBSTVCLDhEQUFDVjtzR0FBYzs7OEVBQ2IsOERBQUNlO29FQUNDUCxNQUFLO29FQUNMUyxTQUFTLElBQU03RSxtQkFBbUI7OEdBQ3hCOzhFQUNYOzs7Ozs7OEVBR0QsOERBQUMyRTtvRUFDQ1AsTUFBSzs4R0FDSzs4RUFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBVVZuRSxpQkFBaUJFLDZCQUNoQiw4REFBQ3lEO2tGQUFjO2tEQUNiLDRFQUFDQTtzRkFBYzs7OERBQ2IsOERBQUNNOzhGQUFhOzhEQUE2Qjs7Ozs7OzhEQUMzQyw4REFBQ3VCO29EQUFLQyxVQUFVLENBQUNuRTt3REFDZkEsRUFBRW9FLGNBQWM7d0RBQ2hCLE1BQU1DLFdBQVcsSUFBSUMsU0FBU3RFLEVBQUVpRCxNQUFNO3dEQUN0Q3hCLFdBQVc7NERBQ1RwQixVQUFVZ0UsU0FBU0UsR0FBRyxDQUFDOzREQUN2QmpFLE9BQU8rRCxTQUFTRSxHQUFHLENBQUM7NERBQ3BCaEUsV0FBVzhELFNBQVNFLEdBQUcsQ0FBQzs0REFDeEJuRixNQUFNaUYsU0FBU0UsR0FBRyxDQUFDOzREQUNuQi9ELFFBQVE2RCxTQUFTRSxHQUFHLENBQUM7d0RBQ3ZCO29EQUNGOzs7c0VBQ0UsOERBQUNsQztzR0FBYzs7OEVBQ2IsOERBQUNBOzs7c0ZBQ0MsOERBQUNvQztzSEFBZ0I7c0ZBQTBDOzs7Ozs7c0ZBQzNELDhEQUFDN0I7NEVBQ0NULE1BQUs7NEVBQ0xVLE1BQUs7NEVBQ0wrQixjQUFjaEcsWUFBWXlCLFFBQVE7NEVBQ2xDcUUsUUFBUTtzSEFDRTs7Ozs7Ozs7Ozs7OzhFQUdkLDhEQUFDckM7OztzRkFDQyw4REFBQ29DO3NIQUFnQjtzRkFBMEM7Ozs7OztzRkFDM0QsOERBQUM3Qjs0RUFDQ1QsTUFBSzs0RUFDTFUsTUFBSzs0RUFDTCtCLGNBQWNoRyxZQUFZMEIsS0FBSzs0RUFDL0JvRSxRQUFRO3NIQUNFOzs7Ozs7Ozs7Ozs7OEVBR2QsOERBQUNyQzs7O3NGQUNDLDhEQUFDb0M7c0hBQWdCO3NGQUEwQzs7Ozs7O3NGQUMzRCw4REFBQzdCOzRFQUNDVCxNQUFLOzRFQUNMVSxNQUFLOzRFQUNMK0IsY0FBY2hHLFlBQVkyQixTQUFTOzRFQUNuQ21FLFFBQVE7c0hBQ0U7Ozs7Ozs7Ozs7Ozs4RUFHZCw4REFBQ3JDOzs7c0ZBQ0MsOERBQUNvQztzSEFBZ0I7c0ZBQTBDOzs7Ozs7c0ZBQzNELDhEQUFDdkI7NEVBQ0NmLE1BQUs7NEVBQ0x5QyxjQUFjaEcsWUFBWVEsSUFBSTs0RUFDOUJzRixRQUFRO3NIQUNFOzs4RkFFViw4REFBQ3ZCO29GQUFPSixPQUFNOzs4RkFBZTs7Ozs7OzhGQUM3Qiw4REFBQ0k7b0ZBQU9KLE9BQU07OzhGQUFnQjs7Ozs7OzhGQUM5Qiw4REFBQ0k7b0ZBQU9KLE9BQU07OzhGQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBRzFCLDhEQUFDVjs7O3NGQUNDLDhEQUFDb0M7c0hBQWdCO3NGQUEwQzs7Ozs7O3NGQUMzRCw4REFBQ3ZCOzRFQUNDZixNQUFLOzRFQUNMeUMsY0FBY2hHLFlBQVk0QixNQUFNOzRFQUNoQ2tFLFFBQVE7c0hBQ0U7OzhGQUVWLDhEQUFDdkI7b0ZBQU9KLE9BQU07OzhGQUFTOzs7Ozs7OEZBQ3ZCLDhEQUFDSTtvRkFBT0osT0FBTTs7OEZBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRUFJL0IsOERBQUNWO3NHQUFjOzs4RUFDYiw4REFBQ2U7b0VBQ0NQLE1BQUs7b0VBQ0xTLFNBQVM7d0VBQ1AzRSxpQkFBaUI7d0VBQ2pCRSxlQUFlO29FQUNqQjs4R0FDVTs4RUFDWDs7Ozs7OzhFQUdELDhEQUFDdUU7b0VBQ0NQLE1BQUs7OEdBQ0s7OEVBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBY3pCO0dBbm1CUy9FOztRQUNVWCwwREFBT0E7OztLQURqQlc7QUFxbUJULCtEQUFlLE1BQUFaLCtEQUFRQSxDQUFDWSxlQUFlQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9wYWdlcy9hZG1pbi91c2Vycy50c3g/ZGM2MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgSGVhZCBmcm9tICduZXh0L2hlYWQnXG5pbXBvcnQgeyB3aXRoQXV0aCwgdXNlQXV0aCB9IGZyb20gJy4uLy4uL2NvbnRleHRzL0F1dGhDb250ZXh0J1xuaW1wb3J0IE5hdmlnYXRpb24gZnJvbSAnLi4vLi4vY29tcG9uZW50cy9OYXZpZ2F0aW9uJ1xuaW1wb3J0IHtcbiAgVXNlcnMsXG4gIFVzZXJQbHVzLFxuICBTZWFyY2gsXG4gIEZpbHRlcixcbiAgTW9yZUhvcml6b250YWwsXG4gIEVkaXQsXG4gIFRyYXNoMixcbiAgU2hpZWxkLFxuICBTZXR0aW5ncyxcbiAgUmVmcmVzaEN3XG59IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuaW50ZXJmYWNlIFVzZXIge1xuICBpZDogc3RyaW5nXG4gIHVzZXJuYW1lOiBzdHJpbmdcbiAgZW1haWw6IHN0cmluZ1xuICBmdWxsX25hbWU6IHN0cmluZ1xuICByb2xlOiBzdHJpbmdcbiAgc3RhdHVzOiBzdHJpbmdcbiAgY3JlYXRlZF9hdDogc3RyaW5nXG4gIGxhc3RfbG9naW46IHN0cmluZ1xuICBhc3NpZ25lZF9hY2NvdW50czogbnVtYmVyXG59XG5cbmZ1bmN0aW9uIFVzZXJNYW5hZ2VtZW50KCkge1xuICBjb25zdCB7IHVzZXIgfSA9IHVzZUF1dGgoKVxuICBjb25zdCBbdXNlcnMsIHNldFVzZXJzXSA9IHVzZVN0YXRlPFVzZXJbXT4oW10pXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtzZWFyY2hUZXJtLCBzZXRTZWFyY2hUZXJtXSA9IHVzZVN0YXRlKCcnKVxuICBjb25zdCBbcm9sZUZpbHRlciwgc2V0Um9sZUZpbHRlcl0gPSB1c2VTdGF0ZSgnQWxsIFJvbGVzJylcbiAgY29uc3QgW3Nob3dDcmVhdGVNb2RhbCwgc2V0U2hvd0NyZWF0ZU1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbc2hvd0VkaXRNb2RhbCwgc2V0U2hvd0VkaXRNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2VkaXRpbmdVc2VyLCBzZXRFZGl0aW5nVXNlcl0gPSB1c2VTdGF0ZTxVc2VyIHwgbnVsbD4obnVsbClcblxuICBjb25zdCB1c2VyU3RhdHMgPSB7XG4gICAgdG90YWw6IHVzZXJzLmxlbmd0aCxcbiAgICBhZG1pbjogdXNlcnMuZmlsdGVyKHUgPT4gdS5yb2xlID09PSAnYWRtaW4nKS5sZW5ndGgsXG4gICAgaW5mcmE6IHVzZXJzLmZpbHRlcih1ID0+IHUucm9sZSA9PT0gJ2NyZXNlbnRfdXNlcicpLmxlbmd0aCxcbiAgICBtYXJrZXRpbmc6IHVzZXJzLmZpbHRlcih1ID0+IHUucm9sZSA9PT0gJ2FpcmxpbmVzX3VzZXInKS5sZW5ndGhcbiAgfVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZFVzZXJzKClcbiAgfSwgW10pXG5cbiAgY29uc3QgbG9hZFVzZXJzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpXG5cbiAgICAgIC8vIFRyeSB0byBmZXRjaCByZWFsIHVzZXJzIGZyb20gQVBJXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3YxL2FkbWluL3VzZXJzJywge1xuICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke2xvY2FsU3RvcmFnZS5nZXRJdGVtKCdzZXNzaW9uX3Rva2VuJyl9YFxuICAgICAgICAgIH1cbiAgICAgICAgfSlcblxuICAgICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICAgICAgc2V0VXNlcnMoZGF0YS51c2VycyB8fCBbXSlcbiAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICBjb25zb2xlLmxvZygnVXNlcnMgQVBJIG5vdCBhdmFpbGFibGUsIHVzaW5nIG1vY2sgZGF0YScpXG4gICAgICB9XG5cbiAgICAgIC8vIEZhbGxiYWNrIHRvIG1vY2sgZGF0YVxuICAgICAgY29uc3QgbW9ja1VzZXJzOiBVc2VyW10gPSBbXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJzEnLFxuICAgICAgICAgIHVzZXJuYW1lOiAnYWRtaW4nLFxuICAgICAgICAgIGVtYWlsOiAnYWRtaW5AZXhhbXBsZS5jb20nLFxuICAgICAgICAgIGZ1bGxfbmFtZTogJ1N5c3RlbSBBZG1pbmlzdHJhdG9yJyxcbiAgICAgICAgICByb2xlOiAnYWRtaW4nLFxuICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsXG4gICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDFUMDA6MDA6MDBaJyxcbiAgICAgICAgICBsYXN0X2xvZ2luOiAnMjAyNC0wMS0wOFQxMDozMDowMFonLFxuICAgICAgICAgIGFzc2lnbmVkX2FjY291bnRzOiA1XG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJzInLFxuICAgICAgICAgIHVzZXJuYW1lOiAnY3Jlc2VudF9yZXZpZXdlcicsXG4gICAgICAgICAgZW1haWw6ICdjcmVzZW50QGV4YW1wbGUuY29tJyxcbiAgICAgICAgICBmdWxsX25hbWU6ICdDcmVzZW50IFJldmlld2VyJyxcbiAgICAgICAgICByb2xlOiAnY3Jlc2VudF91c2VyJyxcbiAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLFxuICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAyVDAwOjAwOjAwWicsXG4gICAgICAgICAgbGFzdF9sb2dpbjogJzIwMjQtMDEtMDhUMDk6MTU6MDBaJyxcbiAgICAgICAgICBhc3NpZ25lZF9hY2NvdW50czogM1xuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICczJyxcbiAgICAgICAgICB1c2VybmFtZTogJ2FpcmxpbmVfYXBwcm92ZXInLFxuICAgICAgICAgIGVtYWlsOiAnYWlybGluZUBleGFtcGxlLmNvbScsXG4gICAgICAgICAgZnVsbF9uYW1lOiAnQWlybGluZSBBcHByb3ZlcicsXG4gICAgICAgICAgcm9sZTogJ2FpcmxpbmVzX3VzZXInLFxuICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsXG4gICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDNUMDA6MDA6MDBaJyxcbiAgICAgICAgICBsYXN0X2xvZ2luOiAnMjAyNC0wMS0wN1QxNjo0NTowMFonLFxuICAgICAgICAgIGFzc2lnbmVkX2FjY291bnRzOiAyXG4gICAgICAgIH1cbiAgICAgIF1cbiAgICAgIHNldFVzZXJzKG1vY2tVc2VycylcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgdXNlcnM6JywgZXJyb3IpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgY3JlYXRlVXNlciA9IGFzeW5jICh1c2VyRGF0YTogUGFydGlhbDxVc2VyPikgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3YxL2FkbWluL3VzZXJzJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7bG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Nlc3Npb25fdG9rZW4nKX1gXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHVzZXJEYXRhKVxuICAgICAgfSlcblxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGFsZXJ0KCdVc2VyIGNyZWF0ZWQgc3VjY2Vzc2Z1bGx5IScpXG4gICAgICAgIGxvYWRVc2VycygpXG4gICAgICAgIHNldFNob3dDcmVhdGVNb2RhbChmYWxzZSlcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGFsZXJ0KCdGYWlsZWQgdG8gY3JlYXRlIHVzZXInKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyB1c2VyOicsIGVycm9yKVxuICAgICAgYWxlcnQoJ0Vycm9yIGNyZWF0aW5nIHVzZXInKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGRlbGV0ZVVzZXIgPSBhc3luYyAodXNlcklkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIWNvbmZpcm0oJ0FyZSB5b3Ugc3VyZSB5b3Ugd2FudCB0byBkZWxldGUgdGhpcyB1c2VyPycpKSByZXR1cm5cblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL3YxL2FkbWluL3VzZXJzLyR7dXNlcklkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnREVMRVRFJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke2xvY2FsU3RvcmFnZS5nZXRJdGVtKCdzZXNzaW9uX3Rva2VuJyl9YFxuICAgICAgICB9XG4gICAgICB9KVxuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgYWxlcnQoJ1VzZXIgZGVsZXRlZCBzdWNjZXNzZnVsbHkhJylcbiAgICAgICAgbG9hZFVzZXJzKClcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGFsZXJ0KCdGYWlsZWQgdG8gZGVsZXRlIHVzZXInKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkZWxldGluZyB1c2VyOicsIGVycm9yKVxuICAgICAgYWxlcnQoJ0Vycm9yIGRlbGV0aW5nIHVzZXInKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGVkaXRVc2VyID0gYXN5bmMgKHVzZXJJZDogc3RyaW5nKSA9PiB7XG4gICAgLy8gRmluZCB0aGUgdXNlciB0byBlZGl0XG4gICAgY29uc3QgdXNlciA9IHVzZXJzLmZpbmQodSA9PiB1LmlkID09PSB1c2VySWQpXG4gICAgaWYgKCF1c2VyKSByZXR1cm5cblxuICAgIHNldEVkaXRpbmdVc2VyKHVzZXIpXG4gICAgc2V0U2hvd0VkaXRNb2RhbCh0cnVlKVxuICB9XG5cbiAgY29uc3QgdXBkYXRlVXNlciA9IGFzeW5jICh1c2VyRGF0YTogUGFydGlhbDxVc2VyPikgPT4ge1xuICAgIGlmICghZWRpdGluZ1VzZXIpIHJldHVyblxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvdjEvYWRtaW4vdXNlcnMvJHtlZGl0aW5nVXNlci5pZH1gLCB7XG4gICAgICAgIG1ldGhvZDogJ1BVVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke2xvY2FsU3RvcmFnZS5nZXRJdGVtKCdzZXNzaW9uX3Rva2VuJyl9YFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh1c2VyRGF0YSlcbiAgICAgIH0pXG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBhbGVydCgnVXNlciB1cGRhdGVkIHN1Y2Nlc3NmdWxseSEnKVxuICAgICAgICBzZXRTaG93RWRpdE1vZGFsKGZhbHNlKVxuICAgICAgICBzZXRFZGl0aW5nVXNlcihudWxsKVxuICAgICAgICBsb2FkVXNlcnMoKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgYWxlcnQoJ0ZhaWxlZCB0byB1cGRhdGUgdXNlcicpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIHVzZXI6JywgZXJyb3IpXG4gICAgICBhbGVydCgnRXJyb3IgdXBkYXRpbmcgdXNlcicpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZmlsdGVyZWRVc2VycyA9IHVzZXJzLmZpbHRlcih1c2VyID0+IHtcbiAgICBjb25zdCBtYXRjaGVzU2VhcmNoID0gdXNlci5mdWxsX25hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgdXNlci51c2VybmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKSkgfHxcbiAgICAgICAgICAgICAgICAgICAgICAgICB1c2VyLmVtYWlsLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpKVxuICAgIGNvbnN0IG1hdGNoZXNSb2xlID0gcm9sZUZpbHRlciA9PT0gJ0FsbCBSb2xlcycgfHwgdXNlci5yb2xlID09PSByb2xlRmlsdGVyXG4gICAgcmV0dXJuIG1hdGNoZXNTZWFyY2ggJiYgbWF0Y2hlc1JvbGVcbiAgfSlcblxuICBjb25zdCBnZXRSb2xlQ29sb3IgPSAocm9sZTogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChyb2xlKSB7XG4gICAgICBjYXNlICdhZG1pbic6IHJldHVybiAnYmctcHVycGxlLTEwMCB0ZXh0LXB1cnBsZS04MDAnXG4gICAgICBjYXNlICdjcmVzZW50X3VzZXInOiByZXR1cm4gJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAnXG4gICAgICBjYXNlICdhaXJsaW5lc191c2VyJzogcmV0dXJuICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnXG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0U3RhdHVzQ29sb3IgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnYWN0aXZlJzogcmV0dXJuICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnXG4gICAgICBjYXNlICdpbmFjdGl2ZSc6IHJldHVybiAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAnXG4gICAgICBjYXNlICdwZW5kaW5nJzogcmV0dXJuICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCdcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCdcbiAgICB9XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8SGVhZD5cbiAgICAgICAgPHRpdGxlPlVzZXIgTWFuYWdlbWVudCAtIEFnZW50aWMgT1JNPC90aXRsZT5cbiAgICAgICAgPG1ldGEgbmFtZT1cImRlc2NyaXB0aW9uXCIgY29udGVudD1cIk1hbmFnZSB1c2VycyBhbmQgdGhlaXIgYWNjZXNzIHBlcm1pc3Npb25zXCIgLz5cbiAgICAgIDwvSGVhZD5cblxuICAgICAgPHN0eWxlIGpzeD57YFxuICAgICAgICAuY2FyZCB7IFxuICAgICAgICAgIGJhY2tncm91bmQ6IHdoaXRlOyBcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAwLjVyZW07IFxuICAgICAgICAgIGJveC1zaGFkb3c6IDAgMXB4IDNweCAwIHJnYmEoMCwgMCwgMCwgMC4xKTsgXG4gICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZTdlYjsgXG4gICAgICAgICAgcGFkZGluZzogMS41cmVtOyBcbiAgICAgICAgfVxuICAgICAgICAuYnRuLXByaW1hcnkgeyBcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjMjU2M2ViOyBcbiAgICAgICAgICBjb2xvcjogd2hpdGU7IFxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7IFxuICAgICAgICAgIHBhZGRpbmc6IDAuNXJlbSAxcmVtOyBcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAwLjVyZW07IFxuICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzOyBcbiAgICAgICAgICBib3JkZXI6IG5vbmU7XG4gICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgICB9XG4gICAgICAgIC5idG4tcHJpbWFyeTpob3ZlciB7IFxuICAgICAgICAgIGJhY2tncm91bmQ6ICMxZDRlZDg7IFxuICAgICAgICB9XG4gICAgICAgIC5hcHAtY29udGFpbmVyIHtcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgICAgbWluLWhlaWdodDogMTAwdmg7XG4gICAgICAgIH1cbiAgICAgICAgLmFwcC1ib2R5IHtcbiAgICAgICAgICBtYXJnaW4tdG9wOiA1cmVtO1xuICAgICAgICAgIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA1cmVtKTtcbiAgICAgICAgfVxuICAgICAgICAubWFpbi1jb250ZW50IHtcbiAgICAgICAgICBmbGV4OiAxO1xuICAgICAgICAgIGJhY2tncm91bmQ6ICNmOWZhZmI7XG4gICAgICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICAgIH1cbiAgICAgICAgLnN0YXQtY2FyZCB7XG4gICAgICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogMC41cmVtO1xuICAgICAgICAgIHBhZGRpbmc6IDEuNXJlbTtcbiAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTVlN2ViO1xuICAgICAgICAgIGJveC1zaGFkb3c6IDAgMXB4IDNweCAwIHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgICAgICAgfVxuICAgICAgYH08L3N0eWxlPlxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFwcC1jb250YWluZXJcIj5cbiAgICAgICAgPE5hdmlnYXRpb24gY3VycmVudFBhZ2U9XCJhZG1pblwiIC8+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhcHAtYm9keVwiPlxuICAgICAgICAgIDxtYWluIGNsYXNzTmFtZT1cIm1haW4tY29udGVudFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOCBweS04XCI+XG4gICAgICAgICAgICAgIHsvKiBQYWdlIEhlYWRlciAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5Vc2VyIE1hbmFnZW1lbnQ8L2gyPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5NYW5hZ2UgdXNlcnMgYW5kIHRoZWlyIGFjY2VzcyBwZXJtaXNzaW9uczwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIFVzZXIgU3RhdGlzdGljcyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy00IGdhcC02IG1iLThcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0YXQtY2FyZFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMiBiZy1ibHVlLTEwMCByb3VuZGVkLWxnIG1yLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8VXNlcnMgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LWJsdWUtNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+VG90YWwgVXNlcnM8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+e3VzZXJTdGF0cy50b3RhbH08L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0YXQtY2FyZFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMiBiZy1ncmVlbi0xMDAgcm91bmRlZC1sZyBtci0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFNoaWVsZCBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+QWRtaW4gVXNlcnM8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+e3VzZXJTdGF0cy5hZG1pbn08L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0YXQtY2FyZFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMiBiZy1wdXJwbGUtMTAwIHJvdW5kZWQtbGcgbXItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxVc2VycyBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtcHVycGxlLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPkluZnJhIFVzZXJzPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZFwiPnt1c2VyU3RhdHMuaW5mcmF9PC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzdGF0LWNhcmRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTIgYmctb3JhbmdlLTEwMCByb3VuZGVkLWxnIG1yLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8VXNlcnMgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LW9yYW5nZS02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5NYXJrZXRpbmcgVXNlcnM8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+e3VzZXJTdGF0cy5tYXJrZXRpbmd9PC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogQ29udHJvbHMgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZCBtYi02XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydCBzbTppdGVtcy1jZW50ZXIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPkFsbCBVc2VyczwvaDM+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNCB3LWZ1bGwgc206dy1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFNlYXJjaCBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWFyY2ggdXNlcnMuLi5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFRlcm19XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFRlcm0oZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIHB4LTMgcHktMiB0ZXh0LXNtIHctNjRcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtyb2xlRmlsdGVyfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Um9sZUZpbHRlcihlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIHB4LTMgcHktMiB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJBbGwgUm9sZXNcIj5BbGwgUm9sZXM8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiYWRtaW5cIj5BZG1pbjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJjcmVzZW50X3VzZXJcIj5DcmVzZW50IFVzZXI8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiYWlybGluZXNfdXNlclwiPkFpcmxpbmVzIFVzZXI8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJidG4tcHJpbWFyeSBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8U2V0dGluZ3MgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+Q29tcGFueSBTZXR0aW5nczwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dDcmVhdGVNb2RhbCh0cnVlKX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tcHJpbWFyeSBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPFVzZXJQbHVzIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPkNyZWF0ZSBVc2VyPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17bG9hZFVzZXJzfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0bi1wcmltYXJ5IGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPlJlZnJlc2g8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBVc2VycyBUYWJsZSAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkXCI+XG4gICAgICAgICAgICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTMyIHctMzIgYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDAgbXgtYXV0b1wiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC00IHRleHQtZ3JheS02MDBcIj5Mb2FkaW5nIHVzZXJzLi4uPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICAgIDx0YWJsZSBjbGFzc05hbWU9XCJtaW4tdy1mdWxsIGRpdmlkZS15IGRpdmlkZS1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDx0aGVhZCBjbGFzc05hbWU9XCJiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dHI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5Vc2VyPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlJvbGU8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+U3RhdHVzPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPkNyZWF0ZWQ8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+TGFzdCBMb2dpbjwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5Bc3NpZ25lZCBBY2NvdW50czwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5BY3Rpb25zPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgICAgICAgPC90aGVhZD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGJvZHkgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7ZmlsdGVyZWRVc2Vycy5tYXAoKHVzZXIpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRyIGtleT17dXNlci5pZH0gY2xhc3NOYW1lPVwiaG92ZXI6YmctZ3JheS01MFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgYmctYmx1ZS02MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dXNlci5mdWxsX25hbWUuY2hhckF0KDApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+e3VzZXIuZnVsbF9uYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+e3VzZXIuZW1haWx9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMi41IHB5LTAuNSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSAke2dldFJvbGVDb2xvcih1c2VyLnJvbGUpfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dXNlci5yb2xlLnJlcGxhY2UoJ18nLCAnICcpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIuNSBweS0wLjUgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gJHtnZXRTdGF0dXNDb2xvcih1c2VyLnN0YXR1cyl9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt1c2VyLnN0YXR1c31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bmV3IERhdGUodXNlci5jcmVhdGVkX2F0KS50b0xvY2FsZURhdGVTdHJpbmcoKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dXNlci5sYXN0X2xvZ2luID8gbmV3IERhdGUodXNlci5sYXN0X2xvZ2luKS50b0xvY2FsZURhdGVTdHJpbmcoKSA6ICdOZXZlcid9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3VzZXIuYXNzaWduZWRfYWNjb3VudHN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBlZGl0VXNlcih1c2VyLmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS05MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiRWRpdCBVc2VyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxFZGl0IGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGRlbGV0ZVVzZXIodXNlci5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIGhvdmVyOnRleHQtcmVkLTkwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJEZWxldGUgVXNlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHJhc2gyIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JheS05MDBcIiB0aXRsZT1cIk1vcmUgT3B0aW9uc1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxNb3JlSG9yaXpvbnRhbCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgPC90Ym9keT5cbiAgICAgICAgICAgICAgICAgICAgPC90YWJsZT5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBDcmVhdGUgVXNlciBNb2RhbCAqL31cbiAgICAgICAgICAgICAge3Nob3dDcmVhdGVNb2RhbCAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLWJsYWNrIGJnLW9wYWNpdHktNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgei01MFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHAtNiB3LTk2XCI+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItNFwiPkNyZWF0ZSBOZXcgVXNlcjwvaDM+XG4gICAgICAgICAgICAgICAgICAgIDxmb3JtIG9uU3VibWl0PXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKVxuICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKGUudGFyZ2V0IGFzIEhUTUxGb3JtRWxlbWVudClcbiAgICAgICAgICAgICAgICAgICAgICBjcmVhdGVVc2VyKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHVzZXJuYW1lOiBmb3JtRGF0YS5nZXQoJ3VzZXJuYW1lJykgYXMgc3RyaW5nLFxuICAgICAgICAgICAgICAgICAgICAgICAgZW1haWw6IGZvcm1EYXRhLmdldCgnZW1haWwnKSBhcyBzdHJpbmcsXG4gICAgICAgICAgICAgICAgICAgICAgICBmdWxsX25hbWU6IGZvcm1EYXRhLmdldCgnZnVsbF9uYW1lJykgYXMgc3RyaW5nLFxuICAgICAgICAgICAgICAgICAgICAgICAgcGFzc3dvcmQ6IGZvcm1EYXRhLmdldCgncGFzc3dvcmQnKSBhcyBzdHJpbmcsXG4gICAgICAgICAgICAgICAgICAgICAgICByb2xlOiBmb3JtRGF0YS5nZXQoJ3JvbGUnKSBhcyBzdHJpbmcsXG4gICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnXG4gICAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5Vc2VybmFtZTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dCBuYW1lPVwidXNlcm5hbWVcIiB0eXBlPVwidGV4dFwiIHJlcXVpcmVkIGNsYXNzTmFtZT1cIm10LTEgYmxvY2sgdy1mdWxsIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBweC0zIHB5LTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+RW1haWw8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXQgbmFtZT1cImVtYWlsXCIgdHlwZT1cImVtYWlsXCIgcmVxdWlyZWQgY2xhc3NOYW1lPVwibXQtMSBibG9jayB3LWZ1bGwgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHB4LTMgcHktMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5GdWxsIE5hbWU8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXQgbmFtZT1cImZ1bGxfbmFtZVwiIHR5cGU9XCJ0ZXh0XCIgcmVxdWlyZWQgY2xhc3NOYW1lPVwibXQtMSBibG9jayB3LWZ1bGwgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHB4LTMgcHktMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5QYXNzd29yZDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dCBuYW1lPVwicGFzc3dvcmRcIiB0eXBlPVwicGFzc3dvcmRcIiByZXF1aXJlZCBtaW5MZW5ndGg9ezh9IGNsYXNzTmFtZT1cIm10LTEgYmxvY2sgdy1mdWxsIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBweC0zIHB5LTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMVwiPk1pbmltdW0gOCBjaGFyYWN0ZXJzPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+Um9sZTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3QgbmFtZT1cInJvbGVcIiByZXF1aXJlZCBjbGFzc05hbWU9XCJtdC0xIGJsb2NrIHctZnVsbCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgcHgtMyBweS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImNyZXNlbnRfdXNlclwiPkNyZXNlbnQgVXNlcjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhaXJsaW5lc191c2VyXCI+QWlybGluZXMgVXNlcjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhZG1pblwiPkFkbWluPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIHNwYWNlLXgtMyBtdC02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93Q3JlYXRlTW9kYWwoZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC1ncmF5LTYwMCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgaG92ZXI6YmctZ3JheS01MFwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tcHJpbWFyeVwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIENyZWF0ZSBVc2VyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9mb3JtPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgey8qIEVkaXQgVXNlciBNb2RhbCAqL31cbiAgICAgICAgICAgICAge3Nob3dFZGl0TW9kYWwgJiYgZWRpdGluZ1VzZXIgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjayBiZy1vcGFjaXR5LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHotNTBcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBwLTYgdy05NlwiPlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTRcIj5FZGl0IFVzZXI8L2gzPlxuICAgICAgICAgICAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KClcbiAgICAgICAgICAgICAgICAgICAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YShlLnRhcmdldCBhcyBIVE1MRm9ybUVsZW1lbnQpXG4gICAgICAgICAgICAgICAgICAgICAgdXBkYXRlVXNlcih7XG4gICAgICAgICAgICAgICAgICAgICAgICB1c2VybmFtZTogZm9ybURhdGEuZ2V0KCd1c2VybmFtZScpIGFzIHN0cmluZyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGVtYWlsOiBmb3JtRGF0YS5nZXQoJ2VtYWlsJykgYXMgc3RyaW5nLFxuICAgICAgICAgICAgICAgICAgICAgICAgZnVsbF9uYW1lOiBmb3JtRGF0YS5nZXQoJ2Z1bGxfbmFtZScpIGFzIHN0cmluZyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJvbGU6IGZvcm1EYXRhLmdldCgncm9sZScpIGFzIHN0cmluZyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1czogZm9ybURhdGEuZ2V0KCdzdGF0dXMnKSBhcyBzdHJpbmdcbiAgICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlVzZXJuYW1lPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT1cInVzZXJuYW1lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVmYXVsdFZhbHVlPXtlZGl0aW5nVXNlci51c2VybmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTEgYmxvY2sgdy1mdWxsIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBweC0zIHB5LTJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+RW1haWw8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwiZW1haWxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVmYXVsdFZhbHVlPXtlZGl0aW5nVXNlci5lbWFpbH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTEgYmxvY2sgdy1mdWxsIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBweC0zIHB5LTJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+RnVsbCBOYW1lPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT1cImZ1bGxfbmFtZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlZmF1bHRWYWx1ZT17ZWRpdGluZ1VzZXIuZnVsbF9uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtMSBibG9jayB3LWZ1bGwgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHB4LTMgcHktMlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5Sb2xlPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJyb2xlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZWZhdWx0VmFsdWU9e2VkaXRpbmdVc2VyLnJvbGV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0xIGJsb2NrIHctZnVsbCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgcHgtMyBweS0yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJjcmVzZW50X3VzZXJcIj5DcmVzZW50IFVzZXI8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiYWlybGluZXNfdXNlclwiPkFpcmxpbmVzIFVzZXI8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiYWRtaW5cIj5BZG1pbjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlN0YXR1czwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwic3RhdHVzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZWZhdWx0VmFsdWU9e2VkaXRpbmdVc2VyLnN0YXR1c31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTEgYmxvY2sgdy1mdWxsIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBweC0zIHB5LTJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImFjdGl2ZVwiPkFjdGl2ZTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJpbmFjdGl2ZVwiPkluYWN0aXZlPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIHNwYWNlLXgtMyBtdC02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0U2hvd0VkaXRNb2RhbChmYWxzZSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRFZGl0aW5nVXNlcihudWxsKVxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC1ncmF5LTYwMCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgaG92ZXI6YmctZ3JheS01MFwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tcHJpbWFyeVwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFVwZGF0ZSBVc2VyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9mb3JtPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L21haW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC8+XG4gIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgd2l0aEF1dGgoVXNlck1hbmFnZW1lbnQpXG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJIZWFkIiwid2l0aEF1dGgiLCJ1c2VBdXRoIiwiTmF2aWdhdGlvbiIsIlVzZXJzIiwiVXNlclBsdXMiLCJTZWFyY2giLCJNb3JlSG9yaXpvbnRhbCIsIkVkaXQiLCJUcmFzaDIiLCJTaGllbGQiLCJTZXR0aW5ncyIsIlJlZnJlc2hDdyIsIlVzZXJNYW5hZ2VtZW50IiwidXNlciIsInVzZXJzIiwic2V0VXNlcnMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInNlYXJjaFRlcm0iLCJzZXRTZWFyY2hUZXJtIiwicm9sZUZpbHRlciIsInNldFJvbGVGaWx0ZXIiLCJzaG93Q3JlYXRlTW9kYWwiLCJzZXRTaG93Q3JlYXRlTW9kYWwiLCJzaG93RWRpdE1vZGFsIiwic2V0U2hvd0VkaXRNb2RhbCIsImVkaXRpbmdVc2VyIiwic2V0RWRpdGluZ1VzZXIiLCJ1c2VyU3RhdHMiLCJ0b3RhbCIsImxlbmd0aCIsImFkbWluIiwiZmlsdGVyIiwidSIsInJvbGUiLCJpbmZyYSIsIm1hcmtldGluZyIsImxvYWRVc2VycyIsInJlc3BvbnNlIiwiZmV0Y2giLCJoZWFkZXJzIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsIm9rIiwiZGF0YSIsImpzb24iLCJlIiwiY29uc29sZSIsImxvZyIsIm1vY2tVc2VycyIsImlkIiwidXNlcm5hbWUiLCJlbWFpbCIsImZ1bGxfbmFtZSIsInN0YXR1cyIsImNyZWF0ZWRfYXQiLCJsYXN0X2xvZ2luIiwiYXNzaWduZWRfYWNjb3VudHMiLCJlcnJvciIsImNyZWF0ZVVzZXIiLCJ1c2VyRGF0YSIsIm1ldGhvZCIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwiYWxlcnQiLCJkZWxldGVVc2VyIiwidXNlcklkIiwiY29uZmlybSIsImVkaXRVc2VyIiwiZmluZCIsInVwZGF0ZVVzZXIiLCJmaWx0ZXJlZFVzZXJzIiwibWF0Y2hlc1NlYXJjaCIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJtYXRjaGVzUm9sZSIsImdldFJvbGVDb2xvciIsImdldFN0YXR1c0NvbG9yIiwidGl0bGUiLCJtZXRhIiwibmFtZSIsImNvbnRlbnQiLCJkaXYiLCJjdXJyZW50UGFnZSIsIm1haW4iLCJoMiIsInAiLCJjbGFzc05hbWUiLCJoMyIsImlucHV0IiwidHlwZSIsInBsYWNlaG9sZGVyIiwidmFsdWUiLCJvbkNoYW5nZSIsInRhcmdldCIsInNlbGVjdCIsIm9wdGlvbiIsImJ1dHRvbiIsInNwYW4iLCJvbkNsaWNrIiwidGFibGUiLCJ0aGVhZCIsInRyIiwidGgiLCJ0Ym9keSIsIm1hcCIsInRkIiwiY2hhckF0IiwicmVwbGFjZSIsIkRhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJmb3JtIiwib25TdWJtaXQiLCJwcmV2ZW50RGVmYXVsdCIsImZvcm1EYXRhIiwiRm9ybURhdGEiLCJnZXQiLCJwYXNzd29yZCIsImxhYmVsIiwicmVxdWlyZWQiLCJtaW5MZW5ndGgiLCJkZWZhdWx0VmFsdWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/pages/admin/users.tsx\n"));

/***/ })

});