"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/social-accounts",{

/***/ "./src/pages/social-accounts.tsx":
/*!***************************************!*\
  !*** ./src/pages/social-accounts.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var modularize_import_loader_name_Home_from_default_as_default_join_esm_icons_home_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! modularize-import-loader?name=Home&from=default&as=default&join=../esm/icons/home!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Home&from=default&as=default&join=../esm/icons/home!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_MessageCircle_from_default_as_default_join_esm_icons_message_circle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! modularize-import-loader?name=MessageCircle&from=default&as=default&join=../esm/icons/message-circle!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=MessageCircle&from=default&as=default&join=../esm/icons/message-circle!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_TrendingUp_from_default_as_default_join_esm_icons_trending_up_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! modularize-import-loader?name=TrendingUp&from=default&as=default&join=../esm/icons/trending-up!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=TrendingUp&from=default&as=default&join=../esm/icons/trending-up!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Users_from_default_as_default_join_esm_icons_users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! modularize-import-loader?name=Users&from=default&as=default&join=../esm/icons/users!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Users&from=default&as=default&join=../esm/icons/users!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Settings_from_default_as_default_join_esm_icons_settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! modularize-import-loader?name=Settings&from=default&as=default&join=../esm/icons/settings!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Settings&from=default&as=default&join=../esm/icons/settings!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_LogOut_from_default_as_default_join_esm_icons_log_out_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! modularize-import-loader?name=LogOut&from=default&as=default&join=../esm/icons/log-out!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=LogOut&from=default&as=default&join=../esm/icons/log-out!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Plus_from_default_as_default_join_esm_icons_plus_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! modularize-import-loader?name=Plus&from=default&as=default&join=../esm/icons/plus!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Plus&from=default&as=default&join=../esm/icons/plus!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Edit_from_default_as_default_join_esm_icons_pen_square_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! modularize-import-loader?name=Edit&from=default&as=default&join=../esm/icons/pen-square!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Edit&from=default&as=default&join=../esm/icons/pen-square!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Trash2_from_default_as_default_join_esm_icons_trash_2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! modularize-import-loader?name=Trash2&from=default&as=default&join=../esm/icons/trash-2!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Trash2&from=default&as=default&join=../esm/icons/trash-2!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_CheckCircle_from_default_as_default_join_esm_icons_check_circle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! modularize-import-loader?name=CheckCircle&from=default&as=default&join=../esm/icons/check-circle!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=CheckCircle&from=default&as=default&join=../esm/icons/check-circle!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_XCircle_from_default_as_default_join_esm_icons_x_circle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! modularize-import-loader?name=XCircle&from=default&as=default&join=../esm/icons/x-circle!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=XCircle&from=default&as=default&join=../esm/icons/x-circle!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_AlertTriangle_from_default_as_default_join_esm_icons_alert_triangle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! modularize-import-loader?name=AlertTriangle&from=default&as=default&join=../esm/icons/alert-triangle!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=AlertTriangle&from=default&as=default&join=../esm/icons/alert-triangle!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Twitter_from_default_as_default_join_esm_icons_twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! modularize-import-loader?name=Twitter&from=default&as=default&join=../esm/icons/twitter!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Twitter&from=default&as=default&join=../esm/icons/twitter!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Facebook_from_default_as_default_join_esm_icons_facebook_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! modularize-import-loader?name=Facebook&from=default&as=default&join=../esm/icons/facebook!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Facebook&from=default&as=default&join=../esm/icons/facebook!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Instagram_from_default_as_default_join_esm_icons_instagram_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! modularize-import-loader?name=Instagram&from=default&as=default&join=../esm/icons/instagram!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Instagram&from=default&as=default&join=../esm/icons/instagram!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Linkedin_from_default_as_default_join_esm_icons_linkedin_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! modularize-import-loader?name=Linkedin&from=default&as=default&join=../esm/icons/linkedin!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Linkedin&from=default&as=default&join=../esm/icons/linkedin!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_RefreshCw_from_default_as_default_join_esm_icons_refresh_cw_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! modularize-import-loader?name=RefreshCw&from=default&as=default&join=../esm/icons/refresh-cw!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=RefreshCw&from=default&as=default&join=../esm/icons/refresh-cw!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Globe_from_default_as_default_join_esm_icons_globe_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! modularize-import-loader?name=Globe&from=default&as=default&join=../esm/icons/globe!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Globe&from=default&as=default&join=../esm/icons/globe!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var modularize_import_loader_name_Activity_from_default_as_default_join_esm_icons_activity_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! modularize-import-loader?name=Activity&from=default&as=default&join=../esm/icons/activity!lucide-react */ \"./node_modules/next/dist/build/webpack/loaders/modularize-import-loader.js?name=Activity&from=default&as=default&join=../esm/icons/activity!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction SocialAccounts() {\n    var _user_full_name, _user, _user_username, _user1, _user2, _user3, _user_role, _user4;\n    _s();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { isAdmin } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useRoleAccess)();\n    const [accounts, setAccounts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [newAccount, setNewAccount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        platform: \"twitter\",\n        username: \"\",\n        display_name: \"\",\n        platform_user_id: \"\",\n        api_key: \"\",\n        api_secret: \"\",\n        access_token: \"\",\n        access_token_secret: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        loadSocialAccounts();\n    }, []);\n    const loadSocialAccounts = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/v1/social-accounts\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"session_token\"))\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setAccounts(data.accounts || []);\n            } else {\n                // Mock data for demo\n                setAccounts([\n                    {\n                        id: \"1\",\n                        platform: \"twitter\",\n                        username: \"@company_support\",\n                        display_name: \"Company Support\",\n                        status: \"active\",\n                        last_sync: \"2024-01-15T10:30:00Z\",\n                        followers_count: 15420,\n                        posts_monitored: 1250,\n                        responses_generated: 89,\n                        created_at: \"2024-01-01T00:00:00Z\"\n                    },\n                    {\n                        id: \"2\",\n                        platform: \"facebook\",\n                        username: \"CompanyPage\",\n                        display_name: \"Company Official Page\",\n                        status: \"active\",\n                        last_sync: \"2024-01-15T10:25:00Z\",\n                        followers_count: 8930,\n                        posts_monitored: 567,\n                        responses_generated: 34,\n                        created_at: \"2024-01-01T00:00:00Z\"\n                    },\n                    {\n                        id: \"3\",\n                        platform: \"instagram\",\n                        username: \"@company_official\",\n                        display_name: \"Company Instagram\",\n                        status: \"error\",\n                        last_sync: \"2024-01-14T15:20:00Z\",\n                        followers_count: 23100,\n                        posts_monitored: 890,\n                        responses_generated: 12,\n                        created_at: \"2024-01-01T00:00:00Z\"\n                    }\n                ]);\n            }\n        } catch (error) {\n            console.error(\"Failed to load social accounts:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAddAccount = async ()=>{\n        try {\n            // Prepare data in the format expected by the API\n            const accountData = {\n                platform: newAccount.platform,\n                username: newAccount.username,\n                display_name: newAccount.display_name || newAccount.username,\n                platform_user_id: newAccount.platform_user_id || newAccount.username.replace(\"@\", \"\"),\n                api_credentials: {\n                    api_key: newAccount.api_key,\n                    api_secret: newAccount.api_secret,\n                    access_token: newAccount.access_token,\n                    access_token_secret: newAccount.access_token_secret\n                }\n            };\n            const response = await fetch(\"/api/v1/social-accounts\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"session_token\"))\n                },\n                body: JSON.stringify(accountData)\n            });\n            if (response.ok) {\n                setShowAddModal(false);\n                setNewAccount({\n                    platform: \"twitter\",\n                    username: \"\",\n                    display_name: \"\",\n                    platform_user_id: \"\",\n                    api_key: \"\",\n                    api_secret: \"\",\n                    access_token: \"\",\n                    access_token_secret: \"\"\n                });\n                loadSocialAccounts();\n                alert(\"Social account added successfully!\");\n            } else {\n                const errorData = await response.json();\n                console.error(\"API Error:\", errorData);\n                alert(\"Failed to add account: \".concat(errorData.detail || \"Unknown error\"));\n            }\n        } catch (error) {\n            console.error(\"Failed to add account:\", error);\n            alert(\"Failed to add account. Please try again.\");\n        }\n    };\n    const handleDeleteAccount = async (accountId)=>{\n        if (!confirm(\"Are you sure you want to delete this account?\")) return;\n        try {\n            const response = await fetch(\"/api/v1/social-accounts/\".concat(accountId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"session_token\"))\n                }\n            });\n            if (response.ok) {\n                setAccounts((prev)=>prev.filter((acc)=>acc.id !== accountId));\n            }\n        } catch (error) {\n            console.error(\"Failed to delete account:\", error);\n        }\n    };\n    const handleSyncAccount = async (accountId)=>{\n        try {\n            const response = await fetch(\"/api/v1/social-accounts/\".concat(accountId, \"/sync\"), {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"session_token\"))\n                }\n            });\n            if (response.ok) {\n                loadSocialAccounts();\n            }\n        } catch (error) {\n            console.error(\"Failed to sync account:\", error);\n        }\n    };\n    const getPlatformIcon = (platform)=>{\n        switch(platform){\n            case \"twitter\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Twitter_from_default_as_default_join_esm_icons_twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 30\n                }, this);\n            case \"facebook\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Facebook_from_default_as_default_join_esm_icons_facebook_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 31\n                }, this);\n            case \"instagram\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Instagram_from_default_as_default_join_esm_icons_instagram_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 32\n                }, this);\n            case \"linkedin\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Linkedin_from_default_as_default_join_esm_icons_linkedin_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 31\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Globe_from_default_as_default_join_esm_icons_globe_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"active\":\n                return \"bg-green-100 text-green-800\";\n            case \"inactive\":\n                return \"bg-gray-100 text-gray-800\";\n            case \"error\":\n                return \"bg-red-100 text-red-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_CheckCircle_from_default_as_default_join_esm_icons_check_circle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 29\n                }, this);\n            case \"inactive\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_XCircle_from_default_as_default_join_esm_icons_x_circle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 31\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_AlertTriangle_from_default_as_default_join_esm_icons_alert_triangle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 28\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_XCircle_from_default_as_default_join_esm_icons_x_circle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        className: \"jsx-4d47a9adb6c61a59\",\n                        children: \"Social Accounts - Agentic ORM\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Manage connected social media accounts\",\n                        className: \"jsx-4d47a9adb6c61a59\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"4d47a9adb6c61a59\",\n                children: \".card.jsx-4d47a9adb6c61a59{background:white;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;-webkit-box-shadow:0 1px 3px 0 rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px 0 rgba(0,0,0,.1);box-shadow:0 1px 3px 0 rgba(0,0,0,.1);border:1px solid#e5e7eb;padding:1.5rem}.btn-primary.jsx-4d47a9adb6c61a59{background:#2563eb;color:white;font-weight:500;padding:.5rem 1rem;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;-webkit-transition:all.2s;-moz-transition:all.2s;-o-transition:all.2s;transition:all.2s;border:none;cursor:pointer}.btn-primary.jsx-4d47a9adb6c61a59:hover{background:#1d4ed8}.btn-secondary.jsx-4d47a9adb6c61a59{background:#6b7280;color:white;font-weight:500;padding:.5rem 1rem;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;-webkit-transition:all.2s;-moz-transition:all.2s;-o-transition:all.2s;transition:all.2s;border:none;cursor:pointer}.btn-secondary.jsx-4d47a9adb6c61a59:hover{background:#4b5563}.btn-danger.jsx-4d47a9adb6c61a59{background:#dc2626;color:white;font-weight:500;padding:.5rem 1rem;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;-webkit-transition:all.2s;-moz-transition:all.2s;-o-transition:all.2s;transition:all.2s;border:none;cursor:pointer}.btn-danger.jsx-4d47a9adb6c61a59:hover{background:#b91c1c}.app-container.jsx-4d47a9adb6c61a59{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;min-height:100vh}.app-header.jsx-4d47a9adb6c61a59{position:fixed;top:0;left:0;right:0;z-index:50;background:white;border-bottom:1px solid#e5e7eb;-webkit-box-shadow:0 1px 3px 0 rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px 0 rgba(0,0,0,.1);box-shadow:0 1px 3px 0 rgba(0,0,0,.1);height:5rem}.app-body.jsx-4d47a9adb6c61a59{margin-top:5rem;min-height:-webkit-calc(100vh - 5rem);min-height:-moz-calc(100vh - 5rem);min-height:calc(100vh - 5rem)}.top-nav.jsx-4d47a9adb6c61a59{background:white;border-bottom:1px solid#e5e7eb;height:3rem}.top-nav-menu.jsx-4d47a9adb6c61a59{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;gap:.5rem;height:100%;max-width:80rem;margin:0 auto;padding:0 1rem}.top-nav-item.jsx-4d47a9adb6c61a59{position:relative;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;padding:.75rem 1.5rem;color:#374151;font-weight:500;text-decoration:none;-webkit-transition:all.2s ease-in-out;-moz-transition:all.2s ease-in-out;-o-transition:all.2s ease-in-out;transition:all.2s ease-in-out;cursor:pointer;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:0 .25rem}.top-nav-item.jsx-4d47a9adb6c61a59:hover{background-color:#f3f4f6;color:#1d4ed8}.top-nav-item.active.jsx-4d47a9adb6c61a59{background-color:#dbeafe;color:#1d4ed8;font-weight:600}.top-nav-item-icon.jsx-4d47a9adb6c61a59{width:1.25rem;height:1.25rem;margin-right:.5rem;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}.main-content.jsx-4d47a9adb6c61a59{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;background:#f9fafb;width:100%}.modal.jsx-4d47a9adb6c61a59{position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,.5);display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;z-index:100}.modal-content.jsx-4d47a9adb6c61a59{background:white;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;padding:2rem;max-width:500px;width:90%;max-height:90vh;overflow-y:auto}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"app-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"app-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"max-w-full mx-auto px-4 sm:px-6 lg:px-8 h-16 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"flex justify-between items-center h-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-2xl lg:text-3xl font-bold text-gray-900\",\n                                                children: \"\\uD83E\\uDD16 Agentic ORM\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"flex items-center space-x-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"hidden lg:flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"w-2 h-2 bg-green-400 rounded-full mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"System Online\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-white text-sm font-medium\",\n                                                                    children: ((_user = user) === null || _user === void 0 ? void 0 : (_user_full_name = _user.full_name) === null || _user_full_name === void 0 ? void 0 : _user_full_name.charAt(0)) || ((_user1 = user) === null || _user1 === void 0 ? void 0 : (_user_username = _user1.username) === null || _user_username === void 0 ? void 0 : _user_username.charAt(0)) || \"U\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"font-medium text-gray-900\",\n                                                                        children: ((_user2 = user) === null || _user2 === void 0 ? void 0 : _user2.full_name) || ((_user3 = user) === null || _user3 === void 0 ? void 0 : _user3.username)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                        lineNumber: 405,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-gray-500 capitalize\",\n                                                                        children: (_user4 = user) === null || _user4 === void 0 ? void 0 : (_user_role = _user4.role) === null || _user_role === void 0 ? void 0 : _user_role.replace(\"_\", \" \")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: logout,\n                                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-gray-500 hover:text-gray-700 ml-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_LogOut_from_default_as_default_join_esm_icons_log_out_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"hidden lg:block top-nav\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"top-nav-menu\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/\",\n                                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"top-nav-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Home_from_default_as_default_join_esm_icons_home_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"top-nav-item-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-4d47a9adb6c61a59\",\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/response-management\",\n                                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"top-nav-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_MessageCircle_from_default_as_default_join_esm_icons_message_circle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"top-nav-item-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-4d47a9adb6c61a59\",\n                                                    children: \"Response Management\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"top-nav-item active\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Users_from_default_as_default_join_esm_icons_users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"top-nav-item-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-4d47a9adb6c61a59\",\n                                                    children: \"Social Accounts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/analytics\",\n                                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"top-nav-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_TrendingUp_from_default_as_default_join_esm_icons_trending_up_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"top-nav-item-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-4d47a9adb6c61a59\",\n                                                    children: \"Analytics\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, this),\n                                        isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/admin\",\n                                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"top-nav-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Settings_from_default_as_default_join_esm_icons_settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"top-nav-item-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-4d47a9adb6c61a59\",\n                                                    children: \"Administration\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"app-body\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"main-content\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-2xl font-bold text-gray-900\",\n                                                    children: \"Social Accounts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: loadSocialAccounts,\n                                                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"btn-secondary flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_RefreshCw_from_default_as_default_join_esm_icons_refresh_cw_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-4d47a9adb6c61a59\",\n                                                                    children: \"Refresh\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowAddModal(true),\n                                                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"btn-primary flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Plus_from_default_as_default_join_esm_icons_plus_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                    lineNumber: 474,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-4d47a9adb6c61a59\",\n                                                                    children: \"Add Account\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"card\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"p-2 bg-blue-100 rounded-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Users_from_default_as_default_join_esm_icons_users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-5 h-5 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-4d47a9adb6c61a59\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-sm text-gray-500\",\n                                                                    children: \"Total Accounts\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-xl font-bold\",\n                                                                    children: accounts.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"card\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"p-2 bg-green-100 rounded-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_CheckCircle_from_default_as_default_join_esm_icons_check_circle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-5 h-5 text-green-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-4d47a9adb6c61a59\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-sm text-gray-500\",\n                                                                    children: \"Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-xl font-bold\",\n                                                                    children: accounts.filter((acc)=>acc.status === \"active\").length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"card\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"p-2 bg-yellow-100 rounded-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Activity_from_default_as_default_join_esm_icons_activity_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"w-5 h-5 text-yellow-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                lineNumber: 511,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-4d47a9adb6c61a59\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-sm text-gray-500\",\n                                                                    children: \"Total Followers\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-xl font-bold\",\n                                                                    children: accounts.reduce((sum, acc)=>sum + acc.followers_count, 0).toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                    lineNumber: 515,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"card\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"p-2 bg-purple-100 rounded-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_MessageCircle_from_default_as_default_join_esm_icons_message_circle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-5 h-5 text-purple-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-4d47a9adb6c61a59\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-sm text-gray-500\",\n                                                                    children: \"Responses Generated\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                    lineNumber: 526,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-xl font-bold\",\n                                                                    children: accounts.reduce((sum, acc)=>sum + acc.responses_generated, 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                    lineNumber: 527,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 15\n                                    }, this),\n                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"mt-4 text-gray-600\",\n                                                children: \"Loading accounts...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 17\n                                    }, this) : accounts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Users_from_default_as_default_join_esm_icons_users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"No social accounts connected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-gray-500 mb-4\",\n                                                children: \"Connect your social media accounts to start monitoring and responding to posts.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 19\n                                            }, this),\n                                            isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowAddModal(true),\n                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"btn-primary\",\n                                                children: \"Add Your First Account\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                        children: accounts.map((account)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"card\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"flex items-start justify-between mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"p-2 bg-gray-100 rounded-lg\",\n                                                                        children: getPlatformIcon(account.platform)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4d47a9adb6c61a59\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-lg font-medium text-gray-900\",\n                                                                                children: account.display_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                                lineNumber: 563,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-sm text-gray-500\",\n                                                                                children: account.username\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                                lineNumber: 564,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                        lineNumber: 562,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                lineNumber: 558,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"flex items-center space-x-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getStatusColor(account.status)),\n                                                                    children: [\n                                                                        getStatusIcon(account.status),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"ml-1 capitalize\",\n                                                                            children: account.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                            lineNumber: 570,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"grid grid-cols-3 gap-4 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-lg font-bold text-gray-900\",\n                                                                        children: account.followers_count.toLocaleString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                        lineNumber: 577,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-xs text-gray-500\",\n                                                                        children: \"Followers\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                        lineNumber: 578,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-lg font-bold text-gray-900\",\n                                                                        children: account.posts_monitored\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                        lineNumber: 581,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-xs text-gray-500\",\n                                                                        children: \"Posts Monitored\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                        lineNumber: 582,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                lineNumber: 580,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-lg font-bold text-gray-900\",\n                                                                        children: account.responses_generated\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                        lineNumber: 585,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-xs text-gray-500\",\n                                                                        children: \"Responses\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                        lineNumber: 586,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"flex items-center justify-between text-sm text-gray-500 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-4d47a9adb6c61a59\",\n                                                                children: [\n                                                                    \"Last sync: \",\n                                                                    new Date(account.last_sync).toLocaleString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-4d47a9adb6c61a59\",\n                                                                children: [\n                                                                    \"Added: \",\n                                                                    new Date(account.created_at).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSyncAccount(account.id),\n                                                                className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"btn-secondary flex items-center space-x-1 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_RefreshCw_from_default_as_default_join_esm_icons_refresh_cw_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                        lineNumber: 600,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-4d47a9adb6c61a59\",\n                                                                        children: \"Sync\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                        lineNumber: 601,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"btn-secondary flex items-center space-x-1 text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Edit_from_default_as_default_join_esm_icons_pen_square_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                                lineNumber: 606,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-4d47a9adb6c61a59\",\n                                                                                children: \"Edit\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                                lineNumber: 607,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                        lineNumber: 605,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleDeleteAccount(account.id),\n                                                                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"btn-danger flex items-center space-x-1 text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(modularize_import_loader_name_Trash2_from_default_as_default_join_esm_icons_trash_2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                                lineNumber: 613,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-4d47a9adb6c61a59\",\n                                                                                children: \"Delete\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                                lineNumber: 614,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                                        lineNumber: 609,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, account.id, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                            lineNumber: 455,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 9\n                    }, this),\n                    showAddModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"modal\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"modal-content\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"text-lg font-medium text-gray-900 mb-4\",\n                                    children: \"Add Social Account\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4d47a9adb6c61a59\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Platform\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: newAccount.platform,\n                                                    onChange: (e)=>setNewAccount((prev)=>({\n                                                                ...prev,\n                                                                platform: e.target.value\n                                                            })),\n                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"w-full border border-gray-300 rounded-lg px-3 py-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"twitter\",\n                                                            className: \"jsx-4d47a9adb6c61a59\",\n                                                            children: \"Twitter\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                            lineNumber: 641,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"facebook\",\n                                                            className: \"jsx-4d47a9adb6c61a59\",\n                                                            children: \"Facebook\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"instagram\",\n                                                            className: \"jsx-4d47a9adb6c61a59\",\n                                                            children: \"Instagram\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"linkedin\",\n                                                            className: \"jsx-4d47a9adb6c61a59\",\n                                                            children: \"LinkedIn\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                            lineNumber: 644,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4d47a9adb6c61a59\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Username\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: newAccount.username,\n                                                    onChange: (e)=>setNewAccount((prev)=>({\n                                                                ...prev,\n                                                                username: e.target.value\n                                                            })),\n                                                    placeholder: \"@username or page name\",\n                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"w-full border border-gray-300 rounded-lg px-3 py-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4d47a9adb6c61a59\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"API Key\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 660,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"password\",\n                                                    value: newAccount.api_key,\n                                                    onChange: (e)=>setNewAccount((prev)=>({\n                                                                ...prev,\n                                                                api_key: e.target.value\n                                                            })),\n                                                    placeholder: \"Enter API key\",\n                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"w-full border border-gray-300 rounded-lg px-3 py-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4d47a9adb6c61a59\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"API Secret\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"password\",\n                                                    value: newAccount.api_secret,\n                                                    onChange: (e)=>setNewAccount((prev)=>({\n                                                                ...prev,\n                                                                api_secret: e.target.value\n                                                            })),\n                                                    placeholder: \"Enter API secret\",\n                                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"w-full border border-gray-300 rounded-lg px-3 py-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                            lineNumber: 670,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"flex items-center space-x-3 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleAddAccount,\n                                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"btn-primary\",\n                                            children: \"Add Account\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAddModal(false),\n                                            className: \"jsx-4d47a9adb6c61a59\" + \" \" + \"btn-secondary\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                            lineNumber: 630,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Agentic Social Handler/frontend/src/pages/social-accounts.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(SocialAccounts, \"G0Cs3wih/ceVatn+IbllhiwDpcY=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useRoleAccess\n    ];\n});\n_c = SocialAccounts;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.withAuth)(SocialAccounts));\nvar _c, _c1;\n$RefreshReg$(_c, \"SocialAccounts\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/social-accounts.tsx\n"));

/***/ })

});