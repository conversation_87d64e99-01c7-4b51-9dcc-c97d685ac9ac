{"name": "agentic-social-handler-frontend", "version": "1.0.0", "description": "Frontend for Agentic Social Handler", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.0", "axios": "^1.6.0", "chart.js": "^4.5.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "next": "13.4.19", "postcss": "^8.4.0", "react": "18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "18.2.0", "recharts": "^2.8.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0"}}