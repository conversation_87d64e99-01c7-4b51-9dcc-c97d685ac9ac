import { useState } from 'react'
import { useRouter } from 'next/router'
import { useAuth, useRoleAccess } from '../contexts/AuthContext'
import {
  Home,
  MessageCircle,
  TrendingUp,
  Users,
  Settings,
  LogOut,
  ChevronDown,
  Shield,
  UserCheck,
  Brain,
  Activity,
  User
} from 'lucide-react'

interface NavigationProps {
  currentPage?: string
}

export default function Navigation({ currentPage }: NavigationProps) {
  const router = useRouter()
  const { user, logout } = useAuth()
  const { canApproveResponses, canViewAnalytics, isAdmin } = useRoleAccess()
  const [adminDropdownOpen, setAdminDropdownOpen] = useState(false)

  const handleNavigation = (path: string) => {
    router.push(path)
  }

  const adminMenuItems = [
    { 
      name: 'User Management', 
      path: '/admin/users', 
      icon: UserCheck,
      description: 'Manage users and permissions'
    },
    { 
      name: 'Secure Credentials', 
      path: '/admin/credentials', 
      icon: Shield,
      description: 'Manage API credentials and tokens'
    },
    { 
      name: 'AI Model Management', 
      path: '/admin/models', 
      icon: Brain,
      description: 'Configure AI models and settings'
    },
    { 
      name: 'System Status', 
      path: '/admin/system', 
      icon: Activity,
      description: 'Monitor system health and performance'
    },
    { 
      name: 'My Profile', 
      path: '/admin/profile', 
      icon: User,
      description: 'Manage personal information'
    }
  ]

  return (
    <>
      <style jsx>{`
        .top-nav {
          background: white;
          border-bottom: 1px solid #e5e7eb;
          height: 3rem;
        }
        .top-nav-menu {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          height: 100%;
          max-width: 80rem;
          margin: 0 auto;
          padding: 0 1rem;
        }
        .top-nav-item {
          position: relative;
          display: flex;
          align-items: center;
          padding: 0.75rem 1.5rem;
          color: #374151;
          font-weight: 500;
          text-decoration: none;
          transition: all 0.2s ease-in-out;
          cursor: pointer;
          border-radius: 0.5rem;
          margin: 0 0.25rem;
        }
        .top-nav-item:hover {
          background-color: #f3f4f6;
          color: #1d4ed8;
        }
        .top-nav-item.active {
          background-color: #dbeafe;
          color: #1d4ed8;
          font-weight: 600;
        }
        .top-nav-item-icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
          flex-shrink: 0;
        }
        .dropdown {
          position: relative;
        }
        .dropdown-menu {
          position: absolute;
          top: 100%;
          right: 0;
          background: white;
          border: 1px solid #e5e7eb;
          border-radius: 0.5rem;
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
          min-width: 280px;
          z-index: 50;
          margin-top: 0.5rem;
        }
        .dropdown-item {
          display: flex;
          align-items: center;
          padding: 0.75rem 1rem;
          color: #374151;
          text-decoration: none;
          transition: background-color 0.2s;
          border-bottom: 1px solid #f3f4f6;
        }
        .dropdown-item:last-child {
          border-bottom: none;
        }
        .dropdown-item:hover {
          background-color: #f9fafb;
        }
        .dropdown-item-icon {
          width: 1rem;
          height: 1rem;
          margin-right: 0.75rem;
          color: #6b7280;
        }
        .dropdown-item-content {
          flex: 1;
        }
        .dropdown-item-title {
          font-weight: 500;
          color: #111827;
        }
        .dropdown-item-desc {
          font-size: 0.75rem;
          color: #6b7280;
          margin-top: 0.125rem;
        }
      `}</style>

      {/* Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm h-20">
        {/* Top Header Bar */}
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 h-16 border-b border-gray-200">
          <div className="flex justify-between items-center h-full">
            <div className="flex items-center">
              <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">
                🤖 Agentic ORM
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="hidden lg:flex items-center space-x-4">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                  System Online
                </span>
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">
                      {user?.full_name?.charAt(0) || user?.username?.charAt(0) || 'U'}
                    </span>
                  </div>
                  <div className="text-sm">
                    <p className="font-medium text-gray-900">{user?.full_name || user?.username}</p>
                    <p className="text-gray-500 capitalize">{user?.role?.replace('_', ' ')}</p>
                  </div>
                  <button
                    onClick={logout}
                    className="text-gray-500 hover:text-gray-700 ml-2"
                  >
                    <LogOut className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Top Navigation */}
        <div className="hidden lg:block top-nav">
          <nav className="top-nav-menu">
            <div 
              className={`top-nav-item ${currentPage === 'dashboard' ? 'active' : ''}`}
              onClick={() => handleNavigation('/')}
            >
              <Home className="top-nav-item-icon" />
              <span>Dashboard</span>
            </div>

            <div 
              className={`top-nav-item ${currentPage === 'response-management' ? 'active' : ''}`}
              onClick={() => handleNavigation('/response-management')}
            >
              <MessageCircle className="top-nav-item-icon" />
              <span>Response Management</span>
            </div>

            <div
              className={`top-nav-item ${currentPage === 'social-accounts' ? 'active' : ''}`}
              onClick={() => handleNavigation('/social-accounts')}
            >
              <Users className="top-nav-item-icon" />
              <span>Social Accounts</span>
            </div>

            <div
              className={`top-nav-item ${currentPage === 'agents' ? 'active' : ''}`}
              onClick={() => handleNavigation('/agents')}
            >
              <Brain className="top-nav-item-icon" />
              <span>Agents</span>
            </div>

            {canViewAnalytics() && (
              <div
                className={`top-nav-item ${currentPage === 'analytics' ? 'active' : ''}`}
                onClick={() => handleNavigation('/analytics')}
              >
                <TrendingUp className="top-nav-item-icon" />
                <span>Analytics</span>
              </div>
            )}

            {isAdmin() && (
              <div className="dropdown">
                <div 
                  className={`top-nav-item ${currentPage?.startsWith('admin') ? 'active' : ''}`}
                  onClick={() => setAdminDropdownOpen(!adminDropdownOpen)}
                >
                  <Settings className="top-nav-item-icon" />
                  <span>Administration</span>
                  <ChevronDown className="w-4 h-4 ml-1" />
                </div>
                
                {adminDropdownOpen && (
                  <div className="dropdown-menu">
                    {adminMenuItems.map((item) => (
                      <div
                        key={item.path}
                        className="dropdown-item"
                        onClick={() => {
                          handleNavigation(item.path)
                          setAdminDropdownOpen(false)
                        }}
                      >
                        <item.icon className="dropdown-item-icon" />
                        <div className="dropdown-item-content">
                          <div className="dropdown-item-title">{item.name}</div>
                          <div className="dropdown-item-desc">{item.description}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </nav>
        </div>
      </header>
    </>
  )
}
