import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useRouter } from 'next/router'

interface User {
  id: string
  username: string
  email: string
  full_name: string
  role: string
  status: string
  can_approve_responses: boolean
  can_escalate: boolean
  can_view_analytics: boolean
  created_at: string
  last_login: string | null
}

interface AuthContextType {
  user: User | null
  sessionToken: string | null
  login: (username: string, password: string) => Promise<{ success: boolean; error?: string }>
  logout: () => void
  isAuthenticated: boolean
  loading: boolean
  checkAuth: () => Promise<boolean>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [sessionToken, setSessionToken] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      const token = localStorage.getItem('session_token')
      const userData = localStorage.getItem('current_user')

      if (token && userData) {
        try {
          setSessionToken(token)
          setUser(JSON.parse(userData))
          // Verify session is still valid
          await checkAuthWithToken(token)
        } catch (error) {
          console.error('Error parsing user data:', error)
          clearAuth()
        }
      } else {
        setLoading(false)
      }
    }

    initializeAuth()
  }, [])

  const checkAuthWithToken = async (token: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/v1/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
        setSessionToken(token)
        setLoading(false)
        return true
      } else {
        clearAuth()
        return false
      }
    } catch (error) {
      console.error('Auth check error:', error)
      clearAuth()
      return false
    }
  }

  const checkAuth = async (): Promise<boolean> => {
    const token = sessionToken || localStorage.getItem('session_token')

    if (!token) {
      setLoading(false)
      return false
    }

    return checkAuthWithToken(token)
  }

  const login = async (username: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password })
      })

      const data = await response.json()

      if (response.ok && data.success) {
        // Store session token and user data
        localStorage.setItem('session_token', data.session_token)
        localStorage.setItem('current_user', JSON.stringify(data.user))
        
        setSessionToken(data.session_token)
        setUser(data.user)
        
        return { success: true }
      } else {
        return { 
          success: false, 
          error: data.error || 'Login failed. Please check your credentials.' 
        }
      }
    } catch (error) {
      console.error('Login error:', error)
      return { 
        success: false, 
        error: 'Connection failed. Please check your network and try again.' 
      }
    }
  }

  const logout = async () => {
    try {
      // Call logout endpoint if we have a session token
      if (sessionToken) {
        await fetch('/api/v1/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${sessionToken}`
          }
        })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      clearAuth()
      router.push('/login')
    }
  }

  const clearAuth = () => {
    localStorage.removeItem('session_token')
    localStorage.removeItem('current_user')
    setSessionToken(null)
    setUser(null)
    setLoading(false)
  }

  const value: AuthContextType = {
    user,
    sessionToken,
    login,
    logout,
    isAuthenticated: !!user && !!sessionToken,
    loading,
    checkAuth
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Higher-order component for protecting routes
export function withAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, loading } = useAuth()
    const router = useRouter()

    useEffect(() => {
      if (!loading && !isAuthenticated) {
        router.push('/login')
      }
    }, [isAuthenticated, loading, router])

    if (loading) {
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
      )
    }

    if (!isAuthenticated) {
      return null // Will redirect to login
    }

    return <Component {...props} />
  }
}

// Hook for role-based access control
export function useRoleAccess() {
  const { user } = useAuth()

  const hasRole = (roles: string | string[]): boolean => {
    if (!user) return false
    const roleArray = Array.isArray(roles) ? roles : [roles]
    return roleArray.includes(user.role)
  }

  const canApproveResponses = (): boolean => {
    return user?.can_approve_responses || false
  }

  const canEscalate = (): boolean => {
    return user?.can_escalate || false
  }

  const canViewAnalytics = (): boolean => {
    return user?.can_view_analytics || false
  }

  const isAdmin = (): boolean => {
    return user?.role === 'admin'
  }

  const isCresentUser = (): boolean => {
    return user?.role === 'cresent_user'
  }

  const isAirlineUser = (): boolean => {
    return user?.role === 'airlines_user'
  }

  return {
    user,
    hasRole,
    canApproveResponses,
    canEscalate,
    canViewAnalytics,
    isAdmin,
    isCresentUser,
    isAirlineUser
  }
}
