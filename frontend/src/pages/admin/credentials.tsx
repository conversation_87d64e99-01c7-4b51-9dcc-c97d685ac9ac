import { useState, useEffect } from 'react'
import Head from 'next/head'
import { withAuth } from '../../contexts/AuthContext'
import Navigation from '../../components/Navigation'
import {
  Shield,
  Database,
  Key,
  Plus,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Lock,
  Eye,
  EyeOff,
  MoreHorizontal,
  Edit,
  Trash2
} from 'lucide-react'

interface Credential {
  id: string
  name: string
  platform: string
  status: 'active' | 'inactive' | 'expired'
  validation: 'valid' | 'invalid' | 'pending'
  created: string
  lastUsed?: string
}

function CredentialsManagement() {
  const [credentials, setCredentials] = useState<Credential[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)

  const securityStatus = {
    encryption: 'AES-256 Encrypted',
    storage: 'Database Encrypted',
    masterKey: 'Secure'
  }

  useEffect(() => {
    loadCredentials()
  }, [])

  const loadCredentials = async () => {
    try {
      setLoading(true)
      // Mock data for now - replace with real API call
      const mockCredentials: Credential[] = [
        {
          id: '1',
          name: 'Twitter API Key',
          platform: 'Twitter',
          status: 'active',
          validation: 'valid',
          created: '2024-01-01T00:00:00Z',
          lastUsed: '2024-01-08T10:30:00Z'
        },
        {
          id: '2',
          name: 'Instagram Business API',
          platform: 'Instagram',
          status: 'active',
          validation: 'valid',
          created: '2024-01-02T00:00:00Z',
          lastUsed: '2024-01-07T15:20:00Z'
        },
        {
          id: '3',
          name: 'OpenAI API Key',
          platform: 'OpenAI',
          status: 'active',
          validation: 'valid',
          created: '2024-01-03T00:00:00Z',
          lastUsed: '2024-01-08T09:45:00Z'
        }
      ]
      setCredentials(mockCredentials)
    } catch (error) {
      console.error('Failed to load credentials:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'inactive': return 'bg-gray-100 text-gray-800'
      case 'expired': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getValidationColor = (validation: string) => {
    switch (validation) {
      case 'valid': return 'bg-green-100 text-green-800'
      case 'invalid': return 'bg-red-100 text-red-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <>
      <Head>
        <title>Secure Credential Management - Agentic ORM</title>
        <meta name="description" content="Manage encrypted API credentials and sensitive tokens" />
      </Head>

      <style jsx>{`
        .card { 
          background: white; 
          border-radius: 0.5rem; 
          box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); 
          border: 1px solid #e5e7eb; 
          padding: 1.5rem; 
        }
        .btn-primary { 
          background: #2563eb; 
          color: white; 
          font-weight: 500; 
          padding: 0.5rem 1rem; 
          border-radius: 0.5rem; 
          transition: all 0.2s; 
          border: none;
          cursor: pointer;
        }
        .btn-primary:hover { 
          background: #1d4ed8; 
        }
        .app-container {
          display: flex;
          flex-direction: column;
          min-height: 100vh;
        }
        .app-body {
          margin-top: 5rem;
          min-height: calc(100vh - 5rem);
        }
        .main-content {
          flex: 1;
          background: #f9fafb;
          width: 100%;
        }
        .security-notice {
          background: #fef3c7;
          border: 1px solid #f59e0b;
          border-radius: 0.5rem;
          padding: 1rem;
          margin-bottom: 1.5rem;
        }
      `}</style>

      <div className="app-container">
        <Navigation currentPage="admin" />

        <div className="app-body">
          <main className="main-content">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              {/* Page Header */}
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Secure Credential Management</h2>
                <p className="text-gray-600">Manage encrypted API credentials and sensitive tokens</p>
              </div>

              {/* Security Status */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="card">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-lg mr-3">
                      <Shield className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Encryption Status</p>
                      <p className="text-lg font-semibold text-green-600">{securityStatus.encryption}</p>
                    </div>
                  </div>
                </div>

                <div className="card">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg mr-3">
                      <Database className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Storage</p>
                      <p className="text-lg font-semibold text-blue-600">{securityStatus.storage}</p>
                    </div>
                  </div>
                </div>

                <div className="card">
                  <div className="flex items-center">
                    <div className="p-2 bg-purple-100 rounded-lg mr-3">
                      <Key className="w-5 h-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Master Key</p>
                      <p className="text-lg font-semibold text-purple-600">{securityStatus.masterKey}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Security Notice */}
              <div className="security-notice">
                <div className="flex items-start">
                  <AlertTriangle className="w-5 h-5 text-yellow-600 mr-3 mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-yellow-800 mb-2">Security Notice</h4>
                    <ul className="text-sm text-yellow-700 space-y-1">
                      <li>• All credentials are encrypted using AES-256 encryption</li>
                      <li>• Master encryption key should be stored securely outside the application</li>
                      <li>• Regularly rotate credentials for enhanced security</li>
                      <li>• Monitor access logs for unauthorized credential access</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Stored Credentials */}
              <div className="card">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">Stored Credentials</h3>
                  <div className="flex space-x-3">
                    <select className="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                      <option>All Platforms</option>
                      <option>Twitter</option>
                      <option>Instagram</option>
                      <option>OpenAI</option>
                    </select>
                    <button 
                      onClick={loadCredentials}
                      className="btn-primary flex items-center space-x-2"
                    >
                      <RefreshCw className="w-4 h-4" />
                      <span>Refresh</span>
                    </button>
                  </div>
                </div>

                {loading ? (
                  <div className="text-center py-12">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Loading credentials...</p>
                  </div>
                ) : credentials.length === 0 ? (
                  <div className="text-center py-12">
                    <Key className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No credentials stored</h3>
                    <p className="text-gray-500">Add your first API credential to get started.</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Credential</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Platform</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Validation</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {credentials.map((credential) => (
                          <tr key={credential.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="p-2 bg-blue-100 rounded-lg mr-3">
                                  <Lock className="w-4 h-4 text-blue-600" />
                                </div>
                                <div>
                                  <div className="text-sm font-medium text-gray-900">{credential.name}</div>
                                  <div className="text-sm text-gray-500">••••••••••••••••</div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="text-sm text-gray-900">{credential.platform}</span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(credential.status)}`}>
                                {credential.status}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getValidationColor(credential.validation)}`}>
                                {credential.validation}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(credential.created).toLocaleDateString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex items-center space-x-2">
                                <button className="text-blue-600 hover:text-blue-900">
                                  <Eye className="w-4 h-4" />
                                </button>
                                <button className="text-blue-600 hover:text-blue-900">
                                  <Edit className="w-4 h-4" />
                                </button>
                                <button className="text-red-600 hover:text-red-900">
                                  <Trash2 className="w-4 h-4" />
                                </button>
                                <button className="text-gray-600 hover:text-gray-900">
                                  <MoreHorizontal className="w-4 h-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>
          </main>
        </div>
      </div>
    </>
  )
}

export default withAuth(CredentialsManagement)
