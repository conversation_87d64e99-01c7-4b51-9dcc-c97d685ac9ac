import { useState, useEffect } from 'react'
import Head from 'next/head'
import { withAuth } from '../../contexts/AuthContext'
import Navigation from '../../components/Navigation'
import {
  Brain,
  CheckCircle,
  RefreshCw,
  Download,
  Shield,
  Globe,
  Eye,
  Settings,
  AlertCircle,
  Clock
} from 'lucide-react'

interface ModelInfo {
  name: string
  type: string
  license: string
  version: string
  lastUpdated: string
  dataProcessing: string
  externalServices: string
  autoUpdates: string
  securityVerification: string
  backupSystem: string
}

function AIModelManagement() {
  const [modelInfo, setModelInfo] = useState<ModelInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [updateHistory, setUpdateHistory] = useState<any[]>([])

  const securitySettings = {
    localData: '100% Local',
    noDataSentToCloud: 'No Data Sent to Cloud',
    manualApprovalRequired: 'Manual Approval Required',
    enabled: 'Enabled',
    active: 'Active'
  }

  const performanceMetrics = {
    textModel: 'Phi-3 Mini',
    visionModel: 'Phi-3.5 Vision',
    languages: '22+ Supported',
    imageSupport: 'Enabled',
    privacyStatus: 'Local Only',
    avgResponseTime: '300-500ms'
  }

  useEffect(() => {
    loadModelInfo()
  }, [])

  const loadModelInfo = async () => {
    try {
      setLoading(true)
      // Mock data based on the screenshot
      const mockModelInfo: ModelInfo = {
        name: 'Microsoft Phi-3 Mini',
        type: 'Small Language Model (SLM)',
        license: 'MIT License',
        version: 's4f4cfd8...',
        lastUpdated: '08/07/2025',
        dataProcessing: 'External AI Services',
        externalServices: 'External AI Services',
        autoUpdates: 'Auto Updates',
        securityVerification: 'Security Verification',
        backupSystem: 'Backup System'
      }
      setModelInfo(mockModelInfo)
      setUpdateHistory([])
    } catch (error) {
      console.error('Failed to load model info:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCheckForUpdates = async () => {
    // Mock update check
    alert('Checking for updates...')
  }

  const handleRefreshStatus = async () => {
    await loadModelInfo()
  }

  return (
    <>
      <Head>
        <title>AI Model Management - Agentic ORM</title>
        <meta name="description" content="Configure AI models and settings" />
      </Head>

      <style jsx>{`
        .card { 
          background: white; 
          border-radius: 0.5rem; 
          box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); 
          border: 1px solid #e5e7eb; 
          padding: 1.5rem; 
        }
        .btn-primary { 
          background: #2563eb; 
          color: white; 
          font-weight: 500; 
          padding: 0.5rem 1rem; 
          border-radius: 0.5rem; 
          transition: all 0.2s; 
          border: none;
          cursor: pointer;
        }
        .btn-primary:hover { 
          background: #1d4ed8; 
        }
        .btn-secondary { 
          background: #6b7280; 
          color: white; 
          font-weight: 500; 
          padding: 0.5rem 1rem; 
          border-radius: 0.5rem; 
          transition: all 0.2s; 
          border: none;
          cursor: pointer;
        }
        .btn-secondary:hover { 
          background: #4b5563; 
        }
        .app-container {
          display: flex;
          flex-direction: column;
          min-height: 100vh;
        }
        .app-body {
          margin-top: 5rem;
          min-height: calc(100vh - 5rem);
        }
        .main-content {
          flex: 1;
          background: #f9fafb;
          width: 100%;
        }
        .info-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.75rem 0;
          border-bottom: 1px solid #f3f4f6;
        }
        .info-row:last-child {
          border-bottom: none;
        }
        .status-indicator {
          display: inline-flex;
          align-items: center;
          padding: 0.25rem 0.75rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 500;
        }
        .status-green {
          background-color: #dcfce7;
          color: #166534;
        }
        .status-blue {
          background-color: #dbeafe;
          color: #1e40af;
        }
        .status-orange {
          background-color: #fed7aa;
          color: #c2410c;
        }
      `}</style>

      <div className="app-container">
        <Navigation currentPage="admin" />

        <div className="app-body">
          <main className="main-content">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              {/* Page Header */}
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">AI Model Management</h2>
                <div className="flex justify-between items-center">
                  <p className="text-gray-600">Configure AI models and settings</p>
                  <div className="flex space-x-3">
                    <button 
                      onClick={handleCheckForUpdates}
                      className="btn-primary flex items-center space-x-2"
                    >
                      <Download className="w-4 h-4" />
                      <span>Check for Updates</span>
                    </button>
                    <button 
                      onClick={handleRefreshStatus}
                      className="btn-secondary flex items-center space-x-2"
                    >
                      <RefreshCw className="w-4 h-4" />
                      <span>Refresh Status</span>
                    </button>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Current Model */}
                <div className="card">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Model</h3>
                  
                  {loading ? (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto"></div>
                      <p className="mt-4 text-gray-600">Loading model info...</p>
                    </div>
                  ) : modelInfo ? (
                    <div className="space-y-4">
                      <div className="info-row">
                        <span className="text-sm text-gray-500">Model Name</span>
                        <span className="text-sm font-medium text-gray-900">{modelInfo.name}</span>
                      </div>
                      <div className="info-row">
                        <span className="text-sm text-gray-500">Model Type</span>
                        <span className="text-sm font-medium text-gray-900">{modelInfo.type}</span>
                      </div>
                      <div className="info-row">
                        <span className="text-sm text-gray-500">License</span>
                        <span className="status-indicator status-blue">{modelInfo.license}</span>
                      </div>
                      <div className="info-row">
                        <span className="text-sm text-gray-500">Current Version</span>
                        <span className="text-sm font-medium text-gray-900">{modelInfo.version}</span>
                      </div>
                      <div className="info-row">
                        <span className="text-sm text-gray-500">Last Updated</span>
                        <span className="text-sm font-medium text-gray-900">{modelInfo.lastUpdated}</span>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">Failed to load model information</p>
                    </div>
                  )}
                </div>

                {/* Security & Privacy */}
                <div className="card">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Security & Privacy</h3>
                  
                  <div className="space-y-4">
                    <div className="info-row">
                      <span className="text-sm text-gray-500">Data Processing</span>
                      <span className="status-indicator status-green">{securitySettings.localData}</span>
                    </div>
                    <div className="info-row">
                      <span className="text-sm text-gray-500">External AI Services</span>
                      <span className="status-indicator status-green">{securitySettings.noDataSentToCloud}</span>
                    </div>
                    <div className="info-row">
                      <span className="text-sm text-gray-500">Auto Updates</span>
                      <span className="status-indicator status-orange">{securitySettings.manualApprovalRequired}</span>
                    </div>
                    <div className="info-row">
                      <span className="text-sm text-gray-500">Security Verification</span>
                      <span className="status-indicator status-green">{securitySettings.enabled}</span>
                    </div>
                    <div className="info-row">
                      <span className="text-sm text-gray-500">Backup System</span>
                      <span className="status-indicator status-green">{securitySettings.active}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* AI Performance */}
              <div className="card mt-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-6">AI Performance</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <div className="info-row">
                      <span className="text-sm text-gray-500">Text Model</span>
                      <span className="text-sm font-medium text-blue-600">{performanceMetrics.textModel}</span>
                    </div>
                    <div className="info-row">
                      <span className="text-sm text-gray-500">Vision Model</span>
                      <span className="text-sm font-medium text-purple-600">{performanceMetrics.visionModel}</span>
                    </div>
                  </div>
                  
                  <div>
                    <div className="info-row">
                      <span className="text-sm text-gray-500">Languages</span>
                      <span className="status-indicator status-green">{performanceMetrics.languages}</span>
                    </div>
                    <div className="info-row">
                      <span className="text-sm text-gray-500">Image Support</span>
                      <span className="status-indicator status-green">{performanceMetrics.imageSupport}</span>
                    </div>
                  </div>
                  
                  <div>
                    <div className="info-row">
                      <span className="text-sm text-gray-500">Privacy Status</span>
                      <span className="status-indicator status-green">{performanceMetrics.privacyStatus}</span>
                    </div>
                    <div className="info-row">
                      <span className="text-sm text-gray-500">Avg Response Time</span>
                      <span className="text-sm font-medium text-gray-900">{performanceMetrics.avgResponseTime}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Update History */}
              <div className="card mt-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Update History</h3>
                
                {updateHistory.length === 0 ? (
                  <div className="text-center py-8">
                    <Clock className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h4 className="text-lg font-medium text-gray-900 mb-2">No update history available</h4>
                    <p className="text-gray-500">Model updates and changes will appear here.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {updateHistory.map((update, index) => (
                      <div key={index} className="border-l-4 border-blue-500 pl-4 py-2">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium text-gray-900">{update.title}</h4>
                            <p className="text-sm text-gray-600">{update.description}</p>
                          </div>
                          <span className="text-xs text-gray-500">{update.date}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </main>
        </div>
      </div>
    </>
  )
}

export default withAuth(AIModelManagement)
