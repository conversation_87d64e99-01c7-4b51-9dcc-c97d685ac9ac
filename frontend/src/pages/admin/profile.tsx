import { useState, useEffect } from 'react'
import Head from 'next/head'
import { withAuth, useAuth } from '../../contexts/AuthContext'
import Navigation from '../../components/Navigation'
import {
  User,
  Edit,
  Save,
  X,
  Calendar,
  Mail,
  Shield,
  Clock,
  Key
} from 'lucide-react'

function MyProfile() {
  const { user } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    full_name: '',
    username: '',
    email: '',
    role: ''
  })

  useEffect(() => {
    if (user) {
      setFormData({
        full_name: user.full_name || '',
        username: user.username || '',
        email: user.email || '',
        role: user.role || ''
      })
    }
  }, [user])

  const handleSave = async () => {
    try {
      // Mock save - replace with real API call
      console.log('Saving profile:', formData)
      setIsEditing(false)
      alert('Profile updated successfully!')
    } catch (error) {
      console.error('Failed to save profile:', error)
      alert('Failed to save profile')
    }
  }

  const handleCancel = () => {
    if (user) {
      setFormData({
        full_name: user.full_name || '',
        username: user.username || '',
        email: user.email || '',
        role: user.role || ''
      })
    }
    setIsEditing(false)
  }

  const handleChangePassword = () => {
    const newPassword = prompt('Enter new password:')
    if (newPassword) {
      // Mock password change
      alert('Password changed successfully!')
    }
  }

  return (
    <>
      <Head>
        <title>My Profile - Agentic ORM</title>
        <meta name="description" content="Manage your personal information and account settings" />
      </Head>

      <style jsx>{`
        .card { 
          background: white; 
          border-radius: 0.5rem; 
          box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); 
          border: 1px solid #e5e7eb; 
          padding: 1.5rem; 
        }
        .btn-primary { 
          background: #2563eb; 
          color: white; 
          font-weight: 500; 
          padding: 0.5rem 1rem; 
          border-radius: 0.5rem; 
          transition: all 0.2s; 
          border: none;
          cursor: pointer;
        }
        .btn-primary:hover { 
          background: #1d4ed8; 
        }
        .btn-secondary { 
          background: #6b7280; 
          color: white; 
          font-weight: 500; 
          padding: 0.5rem 1rem; 
          border-radius: 0.5rem; 
          transition: all 0.2s; 
          border: none;
          cursor: pointer;
        }
        .btn-secondary:hover { 
          background: #4b5563; 
        }
        .app-container {
          display: flex;
          flex-direction: column;
          min-height: 100vh;
        }
        .app-body {
          margin-top: 5rem;
          min-height: calc(100vh - 5rem);
        }
        .main-content {
          flex: 1;
          background: #f9fafb;
          width: 100%;
        }
        .form-group {
          margin-bottom: 1rem;
        }
        .form-label {
          display: block;
          font-size: 0.875rem;
          font-weight: 500;
          color: #374151;
          margin-bottom: 0.5rem;
        }
        .form-input {
          width: 100%;
          padding: 0.5rem 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
        }
        .form-input:focus {
          outline: none;
          border-color: #2563eb;
          box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        .form-input:disabled {
          background-color: #f9fafb;
          color: #6b7280;
        }
        .info-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.75rem 0;
          border-bottom: 1px solid #f3f4f6;
        }
        .info-row:last-child {
          border-bottom: none;
        }
      `}</style>

      <div className="app-container">
        <Navigation currentPage="admin" />

        <div className="app-body">
          <main className="main-content">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              {/* Page Header */}
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">My Profile</h2>
                <p className="text-gray-600">Manage your personal information and account settings</p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Profile Picture & Basic Info */}
                <div className="card">
                  <div className="text-center">
                    <div className="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-white text-2xl font-bold">
                        {user?.full_name?.charAt(0) || user?.username?.charAt(0) || 'S'}
                      </span>
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900">{user?.full_name || 'System Administrator'}</h3>
                    <p className="text-gray-500">@{user?.username || 'admin'}</p>
                    <p className="text-gray-500">{user?.email || '<EMAIL>'}</p>
                    
                    <div className="mt-4">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        <Shield className="w-4 h-4 mr-1" />
                        {user?.role?.replace('_', ' ') || 'Admin'}
                      </span>
                    </div>

                    <button 
                      onClick={() => setIsEditing(!isEditing)}
                      className="btn-primary mt-4 flex items-center space-x-2 mx-auto"
                    >
                      <Edit className="w-4 h-4" />
                      <span>Edit Profile</span>
                    </button>
                  </div>
                </div>

                {/* Personal Information */}
                <div className="lg:col-span-2">
                  <div className="card">
                    <div className="flex justify-between items-center mb-6">
                      <h3 className="text-lg font-semibold text-gray-900">Personal Information</h3>
                      {isEditing && (
                        <div className="flex space-x-2">
                          <button 
                            onClick={handleSave}
                            className="btn-primary flex items-center space-x-2"
                          >
                            <Save className="w-4 h-4" />
                            <span>Save</span>
                          </button>
                          <button 
                            onClick={handleCancel}
                            className="btn-secondary flex items-center space-x-2"
                          >
                            <X className="w-4 h-4" />
                            <span>Cancel</span>
                          </button>
                        </div>
                      )}
                    </div>

                    {isEditing ? (
                      <div className="space-y-4">
                        <div className="form-group">
                          <label className="form-label">Full Name</label>
                          <input
                            type="text"
                            className="form-input"
                            value={formData.full_name}
                            onChange={(e) => setFormData({...formData, full_name: e.target.value})}
                          />
                        </div>
                        
                        <div className="form-group">
                          <label className="form-label">Username</label>
                          <input
                            type="text"
                            className="form-input"
                            value={formData.username}
                            onChange={(e) => setFormData({...formData, username: e.target.value})}
                          />
                        </div>
                        
                        <div className="form-group">
                          <label className="form-label">Email Address</label>
                          <input
                            type="email"
                            className="form-input"
                            value={formData.email}
                            onChange={(e) => setFormData({...formData, email: e.target.value})}
                          />
                        </div>
                        
                        <div className="form-group">
                          <label className="form-label">Role</label>
                          <input
                            type="text"
                            className="form-input"
                            value={formData.role}
                            disabled
                          />
                          <p className="text-xs text-gray-500 mt-1">Role cannot be changed by users</p>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="info-row">
                          <span className="text-sm text-gray-500">Full Name</span>
                          <span className="text-sm font-medium text-gray-900">{user?.full_name || 'System Administrator'}</span>
                        </div>
                        <div className="info-row">
                          <span className="text-sm text-gray-500">Username</span>
                          <span className="text-sm font-medium text-gray-900">@{user?.username || 'admin'}</span>
                        </div>
                        <div className="info-row">
                          <span className="text-sm text-gray-500">Email Address</span>
                          <span className="text-sm font-medium text-gray-900">{user?.email || '<EMAIL>'}</span>
                        </div>
                        <div className="info-row">
                          <span className="text-sm text-gray-500">Role</span>
                          <span className="text-sm font-medium text-gray-900 capitalize">{user?.role?.replace('_', ' ') || 'Admin'}</span>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Account Information */}
                  <div className="card mt-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Account Information</h3>
                    
                    <div className="space-y-4">
                      <div className="info-row">
                        <span className="text-sm text-gray-500">Account Status</span>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Active
                        </span>
                      </div>
                      <div className="info-row">
                        <span className="text-sm text-gray-500">Member Since</span>
                        <span className="text-sm font-medium text-gray-900">08/07/2025</span>
                      </div>
                      <div className="info-row">
                        <span className="text-sm text-gray-500">Last Login</span>
                        <span className="text-sm font-medium text-gray-900">Loading...</span>
                      </div>
                      <div className="info-row">
                        <span className="text-sm text-gray-500">Password</span>
                        <button 
                          onClick={handleChangePassword}
                          className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                        >
                          <Key className="w-4 h-4 mr-1" />
                          Change Password
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </>
  )
}

export default withAuth(MyProfile)
