import { useState, useEffect } from 'react'
import Head from 'next/head'
import { withAuth } from '../../contexts/AuthContext'
import Navigation from '../../components/Navigation'
import {
  Activity,
  CheckCircle,
  RefreshCw,
  Clock,
  Database,
  Brain,
  Globe,
  Settings,
  Play,
  Pause,
  Eye,
  BarChart3,
  AlertCircle
} from 'lucide-react'

interface SystemStatus {
  overall: 'operational' | 'degraded' | 'down'
  lastHealthCheck: string
  autoRefresh: boolean
  debugMode: boolean
  liveMonitoring: 'ready' | 'running' | 'disabled'
}

interface ServiceStatus {
  name: string
  status: 'running' | 'stopped' | 'error'
  endpoint: string
  responseTime: string
  lastCheck: string
  details?: string
}

function SystemStatusHealth() {
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    overall: 'operational',
    lastHealthCheck: '18:03:14',
    autoRefresh: false,
    debugMode: false,
    liveMonitoring: 'ready'
  })
  
  const [services, setServices] = useState<ServiceStatus[]>([])
  const [performance, setPerformance] = useState({
    apiLatency: '4.3ms',
    memoryUsage: '2.6GB',
    uptime: '2h 25m',
    load: 'Normal'
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadSystemStatus()
  }, [])

  const loadSystemStatus = async () => {
    try {
      setLoading(true)
      // Mock data based on the screenshot
      const mockServices: ServiceStatus[] = [
        {
          name: 'Backend API',
          status: 'running',
          endpoint: 'http://localhost:8001',
          responseTime: '22ms',
          lastCheck: '18:03:14',
          details: 'Phi-3 Mini'
        },
        {
          name: 'Admin UI',
          status: 'running',
          endpoint: 'http://localhost:3000',
          responseTime: '<10ms',
          lastCheck: '18:03:14',
          details: 'Active (You\'re using it!)'
        },
        {
          name: 'Database',
          status: 'running',
          endpoint: '',
          responseTime: '',
          lastCheck: '',
          details: 'Responses: 0, Posts: 0, Accounts: 0, Pending: 0'
        },
        {
          name: 'AI Model',
          status: 'running',
          endpoint: '',
          responseTime: '',
          lastCheck: '',
          details: 'Model: Phi-3 Mini, Privacy: 100% Local, Status: Ready'
        },
        {
          name: 'Performance',
          status: 'running',
          endpoint: '',
          responseTime: '',
          lastCheck: '',
          details: 'API Latency: 4.3ms, Memory Usage: 2.6GB, Uptime: 2h 25m, Load: Normal'
        }
      ]
      setServices(mockServices)
    } catch (error) {
      console.error('Failed to load system status:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRefreshNow = async () => {
    await loadSystemStatus()
  }

  const toggleAutoRefresh = () => {
    setSystemStatus(prev => ({ ...prev, autoRefresh: !prev.autoRefresh }))
  }

  const toggleDebugMode = () => {
    setSystemStatus(prev => ({ ...prev, debugMode: !prev.debugMode }))
  }

  const handleManualCheck = () => {
    setSystemStatus(prev => ({ ...prev, liveMonitoring: 'running' }))
    setTimeout(() => {
      setSystemStatus(prev => ({ ...prev, liveMonitoring: 'ready' }))
    }, 2000)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
      case 'operational': return 'text-green-600'
      case 'stopped':
      case 'degraded': return 'text-yellow-600'
      case 'error':
      case 'down': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusBg = (status: string) => {
    switch (status) {
      case 'running':
      case 'operational': return 'bg-green-100'
      case 'stopped':
      case 'degraded': return 'bg-yellow-100'
      case 'error':
      case 'down': return 'bg-red-100'
      default: return 'bg-gray-100'
    }
  }

  return (
    <>
      <Head>
        <title>System Status & Health - Agentic ORM</title>
        <meta name="description" content="Monitor and manage all system components in real-time" />
      </Head>

      <style jsx>{`
        .card { 
          background: white; 
          border-radius: 0.5rem; 
          box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); 
          border: 1px solid #e5e7eb; 
          padding: 1.5rem; 
        }
        .btn-primary { 
          background: #2563eb; 
          color: white; 
          font-weight: 500; 
          padding: 0.5rem 1rem; 
          border-radius: 0.5rem; 
          transition: all 0.2s; 
          border: none;
          cursor: pointer;
        }
        .btn-primary:hover { 
          background: #1d4ed8; 
        }
        .app-container {
          display: flex;
          flex-direction: column;
          min-height: 100vh;
        }
        .app-body {
          margin-top: 5rem;
          min-height: calc(100vh - 5rem);
        }
        .main-content {
          flex: 1;
          background: #f9fafb;
          width: 100%;
        }
        .status-indicator {
          display: inline-flex;
          align-items: center;
          padding: 0.25rem 0.75rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 500;
        }
        .toggle-switch {
          position: relative;
          display: inline-block;
          width: 44px;
          height: 24px;
        }
        .toggle-switch input {
          opacity: 0;
          width: 0;
          height: 0;
        }
        .slider {
          position: absolute;
          cursor: pointer;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: #ccc;
          transition: .4s;
          border-radius: 24px;
        }
        .slider:before {
          position: absolute;
          content: "";
          height: 18px;
          width: 18px;
          left: 3px;
          bottom: 3px;
          background-color: white;
          transition: .4s;
          border-radius: 50%;
        }
        input:checked + .slider {
          background-color: #2563eb;
        }
        input:checked + .slider:before {
          transform: translateX(20px);
        }
      `}</style>

      <div className="app-container">
        <Navigation currentPage="admin" />

        <div className="app-body">
          <main className="main-content">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              {/* Page Header */}
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">System Status & Health</h2>
                <p className="text-gray-600">Monitor and manage all system components in real-time</p>
              </div>

              {/* Overall Status */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div className="card">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium text-gray-500">Overall Status</h3>
                    <CheckCircle className={`w-5 h-5 ${getStatusColor(systemStatus.overall)}`} />
                  </div>
                  <p className={`text-lg font-semibold ${getStatusColor(systemStatus.overall)}`}>
                    All Systems Operational
                  </p>
                  <p className="text-xs text-gray-500 mt-1">5 services running normally</p>
                </div>

                <div className="card">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium text-gray-500">Last Health Check</h3>
                    <Clock className="w-5 h-5 text-blue-600" />
                  </div>
                  <p className="text-lg font-semibold text-gray-900">{systemStatus.lastHealthCheck}</p>
                  <button 
                    onClick={handleRefreshNow}
                    className="text-xs text-blue-600 hover:text-blue-800 mt-1 flex items-center"
                  >
                    <RefreshCw className="w-3 h-3 mr-1" />
                    Refresh Now
                  </button>
                </div>

                <div className="card">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium text-gray-500">Auto Refresh</h3>
                    <label className="toggle-switch">
                      <input 
                        type="checkbox" 
                        checked={systemStatus.autoRefresh}
                        onChange={toggleAutoRefresh}
                      />
                      <span className="slider"></span>
                    </label>
                  </div>
                  <p className="text-lg font-semibold text-gray-900">
                    {systemStatus.autoRefresh ? 'Enabled' : 'Disabled'}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">Refresh every 30 seconds</p>
                </div>

                <div className="card">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium text-gray-500">Live Monitoring</h3>
                    <Activity className="w-5 h-5 text-green-600" />
                  </div>
                  <p className="text-lg font-semibold text-green-600 capitalize">{systemStatus.liveMonitoring}</p>
                  <button 
                    onClick={handleManualCheck}
                    className="text-xs text-blue-600 hover:text-blue-800 mt-1 flex items-center"
                  >
                    <Play className="w-3 h-3 mr-1" />
                    Manual Check
                  </button>
                </div>
              </div>

              {/* Debug Mode */}
              <div className="card mb-8">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Debug Mode</h3>
                    <p className="text-sm text-gray-600">Show detailed logs and API calls</p>
                  </div>
                  <div className="flex items-center space-x-4">
                    <label className="toggle-switch">
                      <input 
                        type="checkbox" 
                        checked={systemStatus.debugMode}
                        onChange={toggleDebugMode}
                      />
                      <span className="slider"></span>
                    </label>
                    <span className="text-sm font-medium text-gray-900">
                      {systemStatus.debugMode ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Services Status */}
              <div className="card">
                <h3 className="text-lg font-semibold text-gray-900 mb-6">Service Status</h3>
                
                {loading ? (
                  <div className="text-center py-12">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Loading system status...</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {services.map((service, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <div className={`p-2 rounded-lg ${getStatusBg(service.status)}`}>
                              {service.name === 'Backend API' && <Globe className={`w-4 h-4 ${getStatusColor(service.status)}`} />}
                              {service.name === 'Admin UI' && <Settings className={`w-4 h-4 ${getStatusColor(service.status)}`} />}
                              {service.name === 'Database' && <Database className={`w-4 h-4 ${getStatusColor(service.status)}`} />}
                              {service.name === 'AI Model' && <Brain className={`w-4 h-4 ${getStatusColor(service.status)}`} />}
                              {service.name === 'Performance' && <BarChart3 className={`w-4 h-4 ${getStatusColor(service.status)}`} />}
                            </div>
                            <div>
                              <h4 className="font-medium text-gray-900">{service.name}</h4>
                              <span className={`status-indicator ${getStatusBg(service.status)} ${getStatusColor(service.status)}`}>
                                {service.status}
                              </span>
                            </div>
                          </div>
                          {service.endpoint && (
                            <div className="text-right text-xs text-gray-500">
                              <p>Endpoint: {service.endpoint}</p>
                              <p>Response: {service.responseTime}</p>
                              <p>Last Check: {service.lastCheck}</p>
                            </div>
                          )}
                        </div>
                        
                        {service.details && (
                          <div className="bg-gray-50 rounded p-3 text-sm text-gray-600">
                            {service.details}
                          </div>
                        )}
                        
                        <div className="flex justify-end mt-3 space-x-2">
                          <button className="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                            <Eye className="w-3 h-3 mr-1" />
                            View Logs
                          </button>
                          <button className="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                            <RefreshCw className="w-3 h-3 mr-1" />
                            Reload UI
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </main>
        </div>
      </div>
    </>
  )
}

export default withAuth(SystemStatusHealth)
