import { useState, useEffect } from 'react'
import Head from 'next/head'
import { withAuth, useAuth } from '../../contexts/AuthContext'
import Navigation from '../../components/Navigation'
import {
  Users,
  UserPlus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Shield,
  Settings,
  RefreshCw
} from 'lucide-react'

interface User {
  id: string
  username: string
  email: string
  full_name: string
  role: string
  status: string
  created_at: string
  last_login: string
  assigned_accounts: number
}

function UserManagement() {
  const { user } = useAuth()
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('All Roles')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)

  const userStats = {
    total: users.length,
    admin: users.filter(u => u.role === 'admin').length,
    infra: users.filter(u => u.role === 'cresent_user').length,
    marketing: users.filter(u => u.role === 'airlines_user').length
  }

  useEffect(() => {
    loadUsers()
  }, [])

  const loadUsers = async () => {
    try {
      setLoading(true)

      // Try to fetch real users from API
      try {
        const response = await fetch('/api/v1/admin/users', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('session_token')}`
          }
        })

        if (response.ok) {
          const data = await response.json()
          setUsers(data.users || [])
          return
        }
      } catch (e) {
        console.log('Users API not available, using mock data')
      }

      // Fallback to mock data
      const mockUsers: User[] = [
        {
          id: '1',
          username: 'admin',
          email: '<EMAIL>',
          full_name: 'System Administrator',
          role: 'admin',
          status: 'active',
          created_at: '2024-01-01T00:00:00Z',
          last_login: '2024-01-08T10:30:00Z',
          assigned_accounts: 5
        },
        {
          id: '2',
          username: 'cresent_reviewer',
          email: '<EMAIL>',
          full_name: 'Cresent Reviewer',
          role: 'cresent_user',
          status: 'active',
          created_at: '2024-01-02T00:00:00Z',
          last_login: '2024-01-08T09:15:00Z',
          assigned_accounts: 3
        },
        {
          id: '3',
          username: 'airline_approver',
          email: '<EMAIL>',
          full_name: 'Airline Approver',
          role: 'airlines_user',
          status: 'active',
          created_at: '2024-01-03T00:00:00Z',
          last_login: '2024-01-07T16:45:00Z',
          assigned_accounts: 2
        }
      ]
      setUsers(mockUsers)
    } catch (error) {
      console.error('Failed to load users:', error)
    } finally {
      setLoading(false)
    }
  }

  const createUser = async (userData: Partial<User>) => {
    try {
      const response = await fetch('/api/v1/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('session_token')}`
        },
        body: JSON.stringify(userData)
      })

      if (response.ok) {
        alert('User created successfully!')
        loadUsers()
        setShowCreateModal(false)
      } else {
        alert('Failed to create user')
      }
    } catch (error) {
      console.error('Error creating user:', error)
      alert('Error creating user')
    }
  }

  const deleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user?')) return

    try {
      const response = await fetch(`/api/v1/admin/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('session_token')}`
        }
      })

      if (response.ok) {
        alert('User deleted successfully!')
        loadUsers()
      } else {
        alert('Failed to delete user')
      }
    } catch (error) {
      console.error('Error deleting user:', error)
      alert('Error deleting user')
    }
  }

  const editUser = async (userId: string) => {
    // Find the user to edit
    const user = users.find(u => u.id === userId)
    if (!user) return

    setEditingUser(user)
    setShowEditModal(true)
  }

  const updateUser = async (userData: Partial<User>) => {
    if (!editingUser) return

    try {
      const response = await fetch(`/api/v1/admin/users/${editingUser.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('session_token')}`
        },
        body: JSON.stringify(userData)
      })

      if (response.ok) {
        alert('User updated successfully!')
        setShowEditModal(false)
        setEditingUser(null)
        loadUsers()
      } else {
        alert('Failed to update user')
      }
    } catch (error) {
      console.error('Error updating user:', error)
      alert('Error updating user')
    }
  }

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesRole = roleFilter === 'All Roles' || user.role === roleFilter
    return matchesSearch && matchesRole
  })

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-purple-100 text-purple-800'
      case 'cresent_user': return 'bg-blue-100 text-blue-800'
      case 'airlines_user': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'inactive': return 'bg-red-100 text-red-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <>
      <Head>
        <title>User Management - Agentic ORM</title>
        <meta name="description" content="Manage users and their access permissions" />
      </Head>

      <style jsx>{`
        .card { 
          background: white; 
          border-radius: 0.5rem; 
          box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); 
          border: 1px solid #e5e7eb; 
          padding: 1.5rem; 
        }
        .btn-primary { 
          background: #2563eb; 
          color: white; 
          font-weight: 500; 
          padding: 0.5rem 1rem; 
          border-radius: 0.5rem; 
          transition: all 0.2s; 
          border: none;
          cursor: pointer;
        }
        .btn-primary:hover { 
          background: #1d4ed8; 
        }
        .app-container {
          display: flex;
          flex-direction: column;
          min-height: 100vh;
        }
        .app-body {
          margin-top: 5rem;
          min-height: calc(100vh - 5rem);
        }
        .main-content {
          flex: 1;
          background: #f9fafb;
          width: 100%;
        }
        .stat-card {
          background: white;
          border-radius: 0.5rem;
          padding: 1.5rem;
          border: 1px solid #e5e7eb;
          box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }
      `}</style>

      <div className="app-container">
        <Navigation currentPage="admin" />

        <div className="app-body">
          <main className="main-content">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              {/* Page Header */}
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">User Management</h2>
                <p className="text-gray-600">Manage users and their access permissions</p>
              </div>

              {/* User Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div className="stat-card">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg mr-3">
                      <Users className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Total Users</p>
                      <p className="text-2xl font-bold">{userStats.total}</p>
                    </div>
                  </div>
                </div>

                <div className="stat-card">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-lg mr-3">
                      <Shield className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Admin Users</p>
                      <p className="text-2xl font-bold">{userStats.admin}</p>
                    </div>
                  </div>
                </div>

                <div className="stat-card">
                  <div className="flex items-center">
                    <div className="p-2 bg-purple-100 rounded-lg mr-3">
                      <Users className="w-5 h-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Infra Users</p>
                      <p className="text-2xl font-bold">{userStats.infra}</p>
                    </div>
                  </div>
                </div>

                <div className="stat-card">
                  <div className="flex items-center">
                    <div className="p-2 bg-orange-100 rounded-lg mr-3">
                      <Users className="w-5 h-5 text-orange-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Marketing Users</p>
                      <p className="text-2xl font-bold">{userStats.marketing}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Controls */}
              <div className="card mb-6">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                  <h3 className="text-lg font-semibold text-gray-900">All Users</h3>
                  
                  <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
                    <div className="flex items-center space-x-2">
                      <Search className="w-4 h-4 text-gray-500" />
                      <input
                        type="text"
                        placeholder="Search users..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="border border-gray-300 rounded-lg px-3 py-2 text-sm w-64"
                      />
                    </div>
                    
                    <select
                      value={roleFilter}
                      onChange={(e) => setRoleFilter(e.target.value)}
                      className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
                    >
                      <option value="All Roles">All Roles</option>
                      <option value="admin">Admin</option>
                      <option value="cresent_user">Cresent User</option>
                      <option value="airlines_user">Airlines User</option>
                    </select>

                    <button className="btn-primary flex items-center space-x-2">
                      <Settings className="w-4 h-4" />
                      <span>Company Settings</span>
                    </button>

                    <button
                      onClick={() => setShowCreateModal(true)}
                      className="btn-primary flex items-center space-x-2"
                    >
                      <UserPlus className="w-4 h-4" />
                      <span>Create User</span>
                    </button>

                    <button
                      onClick={loadUsers}
                      className="btn-primary flex items-center space-x-2"
                    >
                      <RefreshCw className="w-4 h-4" />
                      <span>Refresh</span>
                    </button>
                  </div>
                </div>
              </div>

              {/* Users Table */}
              <div className="card">
                {loading ? (
                  <div className="text-center py-12">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Loading users...</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned Accounts</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredUsers.map((user) => (
                          <tr key={user.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                                  <span className="text-white text-sm font-medium">
                                    {user.full_name.charAt(0)}
                                  </span>
                                </div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-gray-900">{user.full_name}</div>
                                  <div className="text-sm text-gray-500">{user.email}</div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                                {user.role.replace('_', ' ')}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>
                                {user.status}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(user.created_at).toLocaleDateString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {user.assigned_accounts}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex items-center space-x-2">
                                <button
                                  onClick={() => editUser(user.id)}
                                  className="text-blue-600 hover:text-blue-900"
                                  title="Edit User"
                                >
                                  <Edit className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={() => deleteUser(user.id)}
                                  className="text-red-600 hover:text-red-900"
                                  title="Delete User"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </button>
                                <button className="text-gray-600 hover:text-gray-900" title="More Options">
                                  <MoreHorizontal className="w-4 h-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>

              {/* Create User Modal */}
              {showCreateModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                  <div className="bg-white rounded-lg p-6 w-96">
                    <h3 className="text-lg font-semibold mb-4">Create New User</h3>
                    <form onSubmit={(e) => {
                      e.preventDefault()
                      const formData = new FormData(e.target as HTMLFormElement)
                      createUser({
                        username: formData.get('username') as string,
                        email: formData.get('email') as string,
                        full_name: formData.get('full_name') as string,
                        password: formData.get('password') as string,
                        role: formData.get('role') as string,
                        status: 'active'
                      })
                    }}>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Username</label>
                          <input name="username" type="text" required className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Email</label>
                          <input name="email" type="email" required className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Full Name</label>
                          <input name="full_name" type="text" required className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Password</label>
                          <input name="password" type="password" required minLength={8} className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" />
                          <p className="text-xs text-gray-500 mt-1">Minimum 8 characters</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Role</label>
                          <select name="role" required className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                            <option value="cresent_user">Cresent User</option>
                            <option value="airlines_user">Airlines User</option>
                            <option value="admin">Admin</option>
                          </select>
                        </div>
                      </div>
                      <div className="flex justify-end space-x-3 mt-6">
                        <button
                          type="button"
                          onClick={() => setShowCreateModal(false)}
                          className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                        >
                          Cancel
                        </button>
                        <button
                          type="submit"
                          className="btn-primary"
                        >
                          Create User
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              )}

              {/* Edit User Modal */}
              {showEditModal && editingUser && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                  <div className="bg-white rounded-lg p-6 w-96">
                    <h3 className="text-lg font-semibold mb-4">Edit User</h3>
                    <form onSubmit={(e) => {
                      e.preventDefault()
                      const formData = new FormData(e.target as HTMLFormElement)
                      updateUser({
                        username: formData.get('username') as string,
                        email: formData.get('email') as string,
                        full_name: formData.get('full_name') as string,
                        role: formData.get('role') as string,
                        status: formData.get('status') as string
                      })
                    }}>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Username</label>
                          <input
                            name="username"
                            type="text"
                            defaultValue={editingUser.username}
                            required
                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Email</label>
                          <input
                            name="email"
                            type="email"
                            defaultValue={editingUser.email}
                            required
                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Full Name</label>
                          <input
                            name="full_name"
                            type="text"
                            defaultValue={editingUser.full_name}
                            required
                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Role</label>
                          <select
                            name="role"
                            defaultValue={editingUser.role}
                            required
                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                          >
                            <option value="cresent_user">Cresent User</option>
                            <option value="airlines_user">Airlines User</option>
                            <option value="admin">Admin</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Status</label>
                          <select
                            name="status"
                            defaultValue={editingUser.status}
                            required
                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                          >
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                          </select>
                        </div>
                      </div>
                      <div className="flex justify-end space-x-3 mt-6">
                        <button
                          type="button"
                          onClick={() => {
                            setShowEditModal(false)
                            setEditingUser(null)
                          }}
                          className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                        >
                          Cancel
                        </button>
                        <button
                          type="submit"
                          className="btn-primary"
                        >
                          Update User
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              )}
            </div>
          </main>
        </div>
      </div>
    </>
  )
}

export default withAuth(UserManagement)
