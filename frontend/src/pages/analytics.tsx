import { useState, useEffect } from 'react'
import Head from 'next/head'
import { withAuth, useAuth, useRoleAccess } from '../contexts/AuthContext'
import Navigation from '../components/Navigation'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement,
} from 'chart.js'
import { Line, Doughnut, Bar } from 'react-chartjs-2'
import {
  BarChart3,
  PieChart,
  Activity,
  Clock,
  MessageSquare,
  ThumbsUp,
  AlertTriangle,
  Download,
  Calendar,
  Filter,
  Brain,
  Users,
  Shield,
  User
} from 'lucide-react'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement
)

interface AnalyticsData {
  response_trends: {
    labels: string[]
    generated: number[]
    approved: number[]
  }
  sentiment_distribution: {
    positive: { count: number; percentage: number }
    neutral: { count: number; percentage: number }
    negative: { count: number; percentage: number }
  }
  platform_performance: {
    [platform: string]: {
      total_posts: number
      response_rate: number
      avg_response_time: number
    }
  }
  ai_performance: {
    accuracy_score: number
    response_quality: number
    context_understanding: number
  }
  recent_activity: Array<{
    id: string
    type: string
    message: string
    timestamp: string
    status: string
  }>
}

function Analytics() {
  const { user, logout } = useAuth()
  const { canViewAnalytics, isAdmin } = useRoleAccess()
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [dateRange, setDateRange] = useState('7d')

  useEffect(() => {
    if (canViewAnalytics()) {
      loadAnalyticsData()
    }
  }, [dateRange])

  const loadAnalyticsData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/v1/analytics?range=${dateRange}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('session_token')}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setAnalyticsData(data)
      }
    } catch (error) {
      console.error('Failed to load analytics:', error)
      // Mock data for demo
      setAnalyticsData({
        response_trends: {
          labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          generated: [12, 19, 8, 15, 22, 18, 25],
          approved: [10, 16, 7, 13, 20, 15, 22]
        },
        sentiment_distribution: {
          positive: { count: 45, percentage: 45 },
          neutral: { count: 35, percentage: 35 },
          negative: { count: 20, percentage: 20 }
        },
        platform_performance: {
          twitter: { total_posts: 150, response_rate: 85, avg_response_time: 1.2 },
          facebook: { total_posts: 89, response_rate: 78, avg_response_time: 2.1 },
          instagram: { total_posts: 67, response_rate: 92, avg_response_time: 0.8 }
        },
        ai_performance: {
          accuracy_score: 94.2,
          response_quality: 91.8,
          context_understanding: 96.5
        },
        recent_activity: [
          { id: '1', type: 'response_approved', message: 'Response approved for @customer_tweet', timestamp: '2024-01-15T10:30:00Z', status: 'success' },
          { id: '2', type: 'response_generated', message: 'New response generated for negative sentiment', timestamp: '2024-01-15T10:25:00Z', status: 'pending' }
        ]
      })
    } finally {
      setLoading(false)
    }
  }

  if (!canViewAnalytics()) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">You don't have permission to view analytics.</p>
        </div>
      </div>
    )
  }

  const responseTrendsData = {
    labels: analyticsData?.response_trends.labels || [],
    datasets: [
      {
        label: 'Responses Generated',
        data: analyticsData?.response_trends.generated || [],
        borderColor: '#3b82f6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true
      },
      {
        label: 'Responses Approved',
        data: analyticsData?.response_trends.approved || [],
        borderColor: '#10b981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.4,
        fill: true
      }
    ]
  }

  const sentimentData = {
    labels: ['Positive', 'Neutral', 'Negative'],
    datasets: [{
      data: [
        analyticsData?.sentiment_distribution.positive.count || 0,
        analyticsData?.sentiment_distribution.neutral.count || 0,
        analyticsData?.sentiment_distribution.negative.count || 0
      ],
      backgroundColor: ['#10b981', '#6b7280', '#ef4444'],
      borderWidth: 0
    }]
  }

  const platformData = {
    labels: Object.keys(analyticsData?.platform_performance || {}),
    datasets: [{
      label: 'Response Rate (%)',
      data: Object.values(analyticsData?.platform_performance || {}).map(p => p.response_rate),
      backgroundColor: ['#3b82f6', '#8b5cf6', '#f59e0b'],
      borderWidth: 0
    }]
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      }
    },
    scales: {
      y: {
        beginAtZero: true
      }
    }
  }

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
      }
    }
  }

  return (
    <>
      <Head>
        <title>Analytics - Agentic ORM</title>
        <meta name="description" content="Analytics and insights dashboard" />
      </Head>

      <style jsx>{`
        .card { 
          background: white; 
          border-radius: 0.5rem; 
          box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); 
          border: 1px solid #e5e7eb; 
          padding: 1.5rem; 
        }
        .btn-primary { 
          background: #2563eb; 
          color: white; 
          font-weight: 500; 
          padding: 0.5rem 1rem; 
          border-radius: 0.5rem; 
          transition: all 0.2s; 
          border: none;
          cursor: pointer;
        }
        .btn-primary:hover { 
          background: #1d4ed8; 
        }
        .app-container {
          display: flex;
          flex-direction: column;
          min-height: 100vh;
        }
        .app-body {
          margin-top: 5rem;
          min-height: calc(100vh - 5rem);
          display: flex;
        }
        .sidebar {
          width: 280px;
          background: white;
          border-right: 1px solid #e5e7eb;
          padding: 1.5rem;
          flex-shrink: 0;
        }
        @media (max-width: 1024px) {
          .sidebar {
            display: none;
          }
        }
        .sidebar-item {
          display: flex;
          align-items: center;
          padding: 0.75rem 1rem;
          color: #374151;
          text-decoration: none;
          border-radius: 0.5rem;
          margin-bottom: 0.5rem;
          transition: all 0.2s;
        }
        .sidebar-item:hover {
          background-color: #f3f4f6;
          color: #1d4ed8;
        }
        .sidebar-item.active {
          background-color: #dbeafe;
          color: #1d4ed8;
          font-weight: 600;
        }
        .sidebar-icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.75rem;
        }
        .top-nav-item.active {
          background-color: #dbeafe;
          color: #1d4ed8;
          font-weight: 600;
        }
        .top-nav-item-icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
          flex-shrink: 0;
        }
        .main-content {
          flex: 1;
          background: #f9fafb;
          width: 100%;
        }
      `}</style>

      <div className="app-container">
        <Navigation currentPage="analytics" />

        {/* Main Content */}
        <div className="app-body">
          {/* Sidebar */}
          <div className="sidebar">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">System Status</h3>

            <div className="sidebar-item active">
              <BarChart3 className="sidebar-icon" />
              <span>Analytics</span>
            </div>

            <div className="sidebar-item">
              <Brain className="sidebar-icon" />
              <span>Model Management</span>
            </div>

            <div className="sidebar-item">
              <Users className="sidebar-icon" />
              <span>User Management</span>
            </div>

            <div className="sidebar-item">
              <Shield className="sidebar-icon" />
              <span>Secure Credentials</span>
            </div>

            <div className="sidebar-item">
              <User className="sidebar-icon" />
              <span>My Profile</span>
            </div>
          </div>

          <main className="flex-1 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              {/* Page Header */}
              <div className="mb-8">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold text-gray-900">Analytics & Insights</h2>
                  <div className="flex items-center space-x-4">
                    <select
                      value={dateRange}
                      onChange={(e) => setDateRange(e.target.value)}
                      className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
                    >
                      <option value="7d">Last 7 days</option>
                      <option value="30d">Last 30 days</option>
                      <option value="90d">Last 90 days</option>
                    </select>
                    <button className="btn-primary flex items-center space-x-2">
                      <Download className="w-4 h-4" />
                      <span>Export</span>
                    </button>
                  </div>
                </div>
              </div>

              {loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-4 text-gray-600">Loading analytics...</p>
                </div>
              ) : (
                <>
                  {/* Key Metrics */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div className="card">
                      <div className="flex items-center">
                        <div className="p-2 bg-blue-100 rounded-lg mr-3">
                          <MessageSquare className="w-5 h-5 text-blue-600" />
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Total Responses</p>
                          <p className="text-xl font-bold">{analyticsData?.response_trends.generated.reduce((a, b) => a + b, 0) || 0}</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="card">
                      <div className="flex items-center">
                        <div className="p-2 bg-green-100 rounded-lg mr-3">
                          <ThumbsUp className="w-5 h-5 text-green-600" />
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Approval Rate</p>
                          <p className="text-xl font-bold">
                            {analyticsData ? Math.round((analyticsData.response_trends.approved.reduce((a, b) => a + b, 0) / analyticsData.response_trends.generated.reduce((a, b) => a + b, 0)) * 100) : 0}%
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="card">
                      <div className="flex items-center">
                        <div className="p-2 bg-purple-100 rounded-lg mr-3">
                          <Activity className="w-5 h-5 text-purple-600" />
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">AI Accuracy</p>
                          <p className="text-xl font-bold">{analyticsData?.ai_performance.accuracy_score || 0}%</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="card">
                      <div className="flex items-center">
                        <div className="p-2 bg-yellow-100 rounded-lg mr-3">
                          <Clock className="w-5 h-5 text-yellow-600" />
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Avg Response Time</p>
                          <p className="text-xl font-bold">1.2s</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Charts */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    {/* Response Trends Chart */}
                    <div className="card">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-medium text-gray-900">Response Trends</h3>
                        <div className="flex items-center space-x-2">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <span className="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                            Live Data
                          </span>
                        </div>
                      </div>
                      <div className="h-64">
                        <Line data={responseTrendsData} options={chartOptions} />
                      </div>
                    </div>

                    {/* Sentiment Distribution Chart */}
                    <div className="card">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-medium text-gray-900">Sentiment Distribution</h3>
                        <div className="text-sm text-gray-500">Last 30 days</div>
                      </div>
                      <div className="h-64">
                        <Doughnut data={sentimentData} options={doughnutOptions} />
                      </div>
                    </div>
                  </div>

                  {/* Platform Performance */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div className="card">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Platform Performance</h3>
                      <div className="h-64">
                        <Bar data={platformData} options={chartOptions} />
                      </div>
                    </div>

                    {/* AI Performance Metrics */}
                    <div className="card">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">AI Performance</h3>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">Accuracy Score</span>
                          <div className="flex items-center">
                            <div className="w-32 bg-gray-200 rounded-full h-2 mr-2">
                              <div className="bg-green-500 h-2 rounded-full" style={{width: `${analyticsData?.ai_performance.accuracy_score || 0}%`}}></div>
                            </div>
                            <span className="text-sm font-medium">{analyticsData?.ai_performance.accuracy_score || 0}%</span>
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">Response Quality</span>
                          <div className="flex items-center">
                            <div className="w-32 bg-gray-200 rounded-full h-2 mr-2">
                              <div className="bg-blue-500 h-2 rounded-full" style={{width: `${analyticsData?.ai_performance.response_quality || 0}%`}}></div>
                            </div>
                            <span className="text-sm font-medium">{analyticsData?.ai_performance.response_quality || 0}%</span>
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">Context Understanding</span>
                          <div className="flex items-center">
                            <div className="w-32 bg-gray-200 rounded-full h-2 mr-2">
                              <div className="bg-purple-500 h-2 rounded-full" style={{width: `${analyticsData?.ai_performance.context_understanding || 0}%`}}></div>
                            </div>
                            <span className="text-sm font-medium">{analyticsData?.ai_performance.context_understanding || 0}%</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Recent Activity */}
                  <div className="card">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
                    <div className="space-y-3">
                      {analyticsData?.recent_activity.map((activity) => (
                        <div key={activity.id} className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg transition-colors">
                          <div className="flex-shrink-0">
                            <div className={`w-2 h-2 rounded-full ${activity.status === 'success' ? 'bg-green-400' : activity.status === 'pending' ? 'bg-yellow-400' : 'bg-red-400'}`}></div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm text-gray-900">{activity.message}</p>
                            <p className="text-xs text-gray-500">{new Date(activity.timestamp).toLocaleString()}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </div>
          </main>
        </div>
      </div>
    </>
  )
}

export default withAuth(Analytics)
