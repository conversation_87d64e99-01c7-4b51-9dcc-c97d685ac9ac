import { NextApiRequest, NextApiResponse } from 'next'

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:3001'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { proxy } = req.query
  
  // Construct the backend URL
  const path = Array.isArray(proxy) ? proxy.join('/') : proxy
  const backendUrl = `${BACKEND_URL}/api/${path}`
  
  // Forward query parameters
  const url = new URL(backendUrl)
  Object.entries(req.query).forEach(([key, value]) => {
    if (key !== 'proxy' && value) {
      if (Array.isArray(value)) {
        value.forEach(v => url.searchParams.append(key, v))
      } else {
        url.searchParams.append(key, value)
      }
    }
  })

  try {
    // Prepare headers
    const headers: HeadersInit = {
      'Content-Type': req.headers['content-type'] || 'application/json',
    }

    // Forward authorization header if present
    if (req.headers.authorization) {
      headers.Authorization = req.headers.authorization
    }

    // Prepare request options
    const requestOptions: RequestInit = {
      method: req.method,
      headers,
    }

    // Add body for POST, PUT, PATCH requests
    if (req.method && ['POST', 'PUT', 'PATCH'].includes(req.method)) {
      requestOptions.body = JSON.stringify(req.body)
    }

    // Make request to backend
    const response = await fetch(url.toString(), requestOptions)
    
    // Get response data
    const data = await response.text()
    
    // Set response status
    res.status(response.status)
    
    // Forward response headers
    response.headers.forEach((value, key) => {
      res.setHeader(key, value)
    })
    
    // Send response
    try {
      // Try to parse as JSON
      const jsonData = JSON.parse(data)
      res.json(jsonData)
    } catch {
      // Send as text if not JSON
      res.send(data)
    }
    
  } catch (error) {
    console.error('Proxy error:', error)
    res.status(500).json({ 
      error: 'Internal server error',
      message: 'Failed to proxy request to backend'
    })
  }
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '10mb',
    },
  },
}
