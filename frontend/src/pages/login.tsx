import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import { BrainCircuit, User, Lock, ShieldCheck, Zap, Users, Activity, Info, LogIn, AlertCircle } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'

export default function Login() {
  const router = useRouter()
  const { login, isAuthenticated, loading: authLoading } = useAuth()
  const [credentials, setCredentials] = useState({
    username: '',
    password: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    // If already authenticated, redirect to dashboard
    if (isAuthenticated && !authLoading) {
      router.push('/')
    }
  }, [isAuthenticated, authLoading, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const result = await login(credentials.username, credentials.password)

      if (result.success) {
        // AuthContext will handle the redirect
        router.push('/')
      } else {
        setError(result.error || 'Login failed. Please check your credentials.')
      }
    } catch (error) {
      setError('Connection failed. Please check your network and try again.')
    } finally {
      setLoading(false)
    }
  }

  // Show loading spinner while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <div className="text-center text-white">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto"></div>
          <p className="mt-4">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>Agentic ORM - Intelligent Social Media Management</title>
        <meta name="description" content="Privacy-focused, intelligent social media automation and customer service management" />
      </Head>

      <div 
        className="min-h-screen flex items-center justify-center p-4"
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }}
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-10 rounded-full animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-white opacity-5 rounded-full animate-pulse"></div>
          <div className="absolute top-1/2 left-1/4 w-32 h-32 bg-white opacity-10 rounded-full animate-pulse"></div>
        </div>

        {/* Main Container */}
        <div className="relative z-10 w-full max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            
            {/* Left Side - Branding & Features */}
            <div className="text-white space-y-8">
              {/* Logo & Brand */}
              <div className="text-center lg:text-left">
                <div className="flex items-center justify-center lg:justify-start mb-6">
                  <div className="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mr-4 animate-pulse">
                    <BrainCircuit className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h1 className="text-4xl font-bold">Agentic ORM</h1>
                    <p className="text-blue-200 text-lg">Intelligent Social Media Management</p>
                  </div>
                </div>
                
                <p className="text-xl text-blue-100 leading-relaxed mb-8">
                  Powered by Microsoft Phi-3 Mini for privacy-focused, intelligent social media automation and customer service management.
                </p>
              </div>

              {/* Key Features */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6 border border-white border-opacity-20">
                  <div className="w-12 h-12 bg-green-500 bg-opacity-20 rounded-lg flex items-center justify-center mb-4">
                    <ShieldCheck className="w-6 h-6 text-green-300" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">100% Privacy</h3>
                  <p className="text-blue-100 text-sm">Complete local processing with Microsoft Phi-3 Mini. Your data never leaves your infrastructure.</p>
                </div>
                
                <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6 border border-white border-opacity-20">
                  <div className="w-12 h-12 bg-purple-500 bg-opacity-20 rounded-lg flex items-center justify-center mb-4">
                    <Zap className="w-6 h-6 text-purple-300" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">AI-Powered</h3>
                  <p className="text-blue-100 text-sm">Advanced sentiment analysis and intelligent response generation for superior customer engagement.</p>
                </div>
                
                <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6 border border-white border-opacity-20">
                  <div className="w-12 h-12 bg-orange-500 bg-opacity-20 rounded-lg flex items-center justify-center mb-4">
                    <Users className="w-6 h-6 text-orange-300" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Role-Based Access</h3>
                  <p className="text-blue-100 text-sm">Admin, Infrastructure, and Marketing roles with granular permissions and account assignments.</p>
                </div>
                
                <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6 border border-white border-opacity-20">
                  <div className="w-12 h-12 bg-blue-500 bg-opacity-20 rounded-lg flex items-center justify-center mb-4">
                    <Activity className="w-6 h-6 text-blue-300" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Real-Time Monitoring</h3>
                  <p className="text-blue-100 text-sm">Live system health monitoring, performance analytics, and comprehensive dashboard insights.</p>
                </div>
              </div>

              {/* Stats */}
              <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6 border border-white border-opacity-20">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-white">10K+</div>
                    <div className="text-blue-200 text-sm">Daily Responses</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-white">99.9%</div>
                    <div className="text-blue-200 text-sm">Uptime</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-white">24/7</div>
                    <div className="text-blue-200 text-sm">Monitoring</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Side - Login Form */}
            <div className="animate-fade-in">
              <div className="bg-white bg-opacity-10 backdrop-blur-lg rounded-2xl p-8 max-w-md mx-auto border border-white border-opacity-20">
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold text-white mb-2">Welcome Back</h2>
                  <p className="text-blue-200">Sign in to access your Agentic ORM dashboard</p>
                </div>

                {/* Login Form */}
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label className="block text-white text-sm font-medium mb-2">Username</label>
                    <div className="relative">
                      <input 
                        type="text" 
                        value={credentials.username}
                        onChange={(e) => setCredentials({...credentials, username: e.target.value})}
                        required 
                        className="w-full px-4 py-3 pl-12 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-transparent"
                        placeholder="Enter your username"
                      />
                      <User className="w-5 h-5 text-blue-200 absolute left-4 top-3.5" />
                    </div>
                  </div>

                  <div>
                    <label className="block text-white text-sm font-medium mb-2">Password</label>
                    <div className="relative">
                      <input 
                        type="password"
                        value={credentials.password}
                        onChange={(e) => setCredentials({...credentials, password: e.target.value})}
                        required 
                        className="w-full px-4 py-3 pl-12 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-transparent"
                        placeholder="Enter your password"
                      />
                      <Lock className="w-5 h-5 text-blue-200 absolute left-4 top-3.5" />
                    </div>
                  </div>

                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  >
                    <span className="flex items-center justify-center">
                      {loading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Signing in...
                        </>
                      ) : (
                        <>
                          <LogIn className="w-5 h-5 mr-2" />
                          Sign In
                        </>
                      )}
                    </span>
                  </button>
                </form>

                {/* Demo Credentials */}
                <div className="mt-8 p-4 bg-blue-900 bg-opacity-30 rounded-lg border border-blue-400 border-opacity-30">
                  <h4 className="text-white font-semibold mb-3 flex items-center">
                    <Info className="w-4 h-4 mr-2" />
                    Demo Credentials
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between items-center">
                      <span className="text-blue-200">Admin:</span>
                      <span className="text-white font-mono">admin / admin123</span>
                    </div>
                    <div className="text-xs text-blue-300 mt-2">
                      Full system access including user management
                    </div>
                  </div>
                </div>

                {/* Error Message */}
                {error && (
                  <div className="mt-4 p-3 bg-red-500 bg-opacity-20 border border-red-400 border-opacity-30 rounded-lg">
                    <div className="flex items-center text-red-200">
                      <AlertCircle className="w-4 h-4 mr-2" />
                      <span>{error}</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Footer */}
              <div className="text-center mt-8 text-blue-200 text-sm">
                <p>© 2024 Agentic ORM. Built with privacy and security in mind.</p>
                <div className="flex items-center justify-center mt-2 space-x-4">
                  <span className="flex items-center">
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                    All systems operational
                  </span>
                  <span>🔒 100% Local Processing</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
