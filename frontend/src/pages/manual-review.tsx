import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import axios from 'axios';
import { CheckCircle, XCircle, Clock, AlertCircle, ExternalLink, User, Building } from 'lucide-react';

interface ResponseVariant {
  id: number;
  content: string;
  tone: string;
  confidence_score: number;
  sentiment_analysis: {
    sentiment: string;
    score: number;
  };
  response_variant_number: number;
  status: string;
}

interface ResponseBatch {
  id: number;
  social_post_id: number;
  batch_status: string;
  total_variants: number;
  requires_airline_approval: boolean;
  created_at: string;
  original_post_content: string;
  original_response_content: string;
  variants: ResponseVariant[];
}

const ManualReview: React.FC = () => {
  const router = useRouter();
  const { batch } = router.query;
  const [batches, setBatches] = useState<ResponseBatch[]>([]);
  const [selectedBatch, setSelectedBatch] = useState<ResponseBatch | null>(null);
  const [selectedVariant, setSelectedVariant] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchBatches();
  }, []);

  useEffect(() => {
    if (batch && batches.length > 0) {
      const batchToOpen = batches.find(b => b.id === parseInt(batch as string));
      if (batchToOpen) {
        setSelectedBatch(batchToOpen);
      }
    }
  }, [batch, batches]);

  const fetchBatches = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axios.get('http://localhost:3001/api/v1/manual-review/batches');
      setBatches((response.data as any)?.batches || []);
    } catch (err: any) {
      const errorMessage = err?.response?.data?.error || err?.message || 'Failed to fetch response batches';
      setError(errorMessage);
      console.error('Error fetching batches:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (variantId: number, approvalType: 'cresent' | 'airline' = 'cresent') => {
    if (!selectedBatch) return;

    try {
      await axios.post(`http://localhost:3001/api/v1/manual-review/variants/${variantId}/approve`, {
        approval_type: approvalType
      });
      await fetchBatches(); // Refresh data
      setSelectedBatch(null); // Close the batch view
    } catch (err) {
      console.error('Error approving variant:', err);
    }
  };

  const handlePassToAirline = async (variantId: number) => {
    if (!selectedBatch) return;

    try {
      await axios.post(`http://localhost:3001/api/v1/manual-review/variants/${variantId}/pass-to-airline`);
      await fetchBatches(); // Refresh data
      setSelectedBatch(null); // Close the batch view
    } catch (err) {
      console.error('Error passing to airline:', err);
    }
  };

  const handleReject = async (batchId: number) => {
    try {
      await axios.post(`http://localhost:3001/api/v1/manual-review/batches/${batchId}/reject`);
      await fetchBatches(); // Refresh data
      setSelectedBatch(null); // Close the batch view
    } catch (err) {
      console.error('Error rejecting batch:', err);
    }
  };

  const getSentimentBadgeClass = (sentiment: string) => {
    switch (sentiment.toLowerCase()) {
      case 'positive': return 'bg-green-100 text-green-800';
      case 'negative': return 'bg-red-100 text-red-800';
      case 'neutral': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending_review': return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'approved': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'rejected': return <XCircle className="w-4 h-4 text-red-500" />;
      default: return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading manual review dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <XCircle className="w-12 h-12 text-red-500 mx-auto" />
          <p className="mt-4 text-red-600">{error}</p>
          <button 
            onClick={fetchBatches}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gray-900">ORM Flow - Manual Review</h1>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                {batches.filter(b => b.batch_status === 'pending_review').length} Pending
              </span>
            </div>
            <button
              onClick={() => window.open('http://localhost:3001/admin-ui/dashboard.html', '_blank')}
              className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              <ExternalLink className="w-4 h-4" />
              <span>Back to Dashboard</span>
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {selectedBatch ? (
          // Batch Detail View
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <button
                onClick={() => setSelectedBatch(null)}
                className="text-blue-600 hover:text-blue-800 flex items-center space-x-2"
              >
                <span>← Back to Batches</span>
              </button>
              <div className="flex items-center space-x-2">
                {getStatusIcon(selectedBatch.batch_status)}
                <span className="text-sm font-medium text-gray-600">
                  Batch #{selectedBatch.id}
                </span>
              </div>
            </div>

            {/* Original Post */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Original Social Media Post</h3>
              <p className="text-gray-700 bg-gray-50 p-4 rounded-lg">{selectedBatch.original_post_content}</p>
            </div>

            {/* Response Variants */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Response Variants ({selectedBatch.total_variants})
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {selectedBatch.variants.map((variant) => (
                  <div
                    key={variant.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      selectedVariant === variant.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-blue-300'
                    }`}
                    onClick={() => setSelectedVariant(variant.id)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-semibold text-gray-900">
                        Variant {variant.response_variant_number}
                      </span>
                      <span className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                        {Math.round(variant.confidence_score * 100)}% confidence
                      </span>
                    </div>
                    <p className="text-sm text-gray-700 mb-3">{variant.content}</p>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-700">
                        {variant.tone}
                      </span>
                      <span className={`text-xs px-2 py-1 rounded-full ${getSentimentBadgeClass(variant.sentiment_analysis.sentiment)}`}>
                        {variant.sentiment_analysis.sentiment}
                      </span>
                    </div>
                  </div>
                ))}
              </div>

              {/* Action Buttons */}
              <div className="flex justify-between items-center mt-6 pt-6 border-t">
                <button
                  onClick={() => handleReject(selectedBatch.id)}
                  className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center space-x-2"
                >
                  <XCircle className="w-4 h-4" />
                  <span>Reject All Variants</span>
                </button>

                {selectedVariant && (
                  <div className="flex space-x-3">
                    {selectedBatch.requires_airline_approval ? (
                      <>
                        <button
                          onClick={() => handlePassToAirline(selectedVariant)}
                          className="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 flex items-center space-x-2"
                        >
                          <Building className="w-4 h-4" />
                          <span>Pass to Airline for Approval</span>
                        </button>
                        <button
                          onClick={() => handleApprove(selectedVariant, 'airline')}
                          className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2"
                        >
                          <CheckCircle className="w-4 h-4" />
                          <span>Final Airline Approval</span>
                        </button>
                      </>
                    ) : (
                      <button
                        onClick={() => handleApprove(selectedVariant, 'cresent')}
                        className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2"
                      >
                        <CheckCircle className="w-4 h-4" />
                        <span>Approve Selected Variant</span>
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : (
          // Batch List View
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Response Batches Pending Review</h2>
              </div>
              <div className="divide-y divide-gray-200">
                {batches.filter(batch => batch.batch_status === 'pending_review').map((batch) => (
                  <div
                    key={batch.id}
                    className="p-6 hover:bg-gray-50 cursor-pointer"
                    onClick={() => setSelectedBatch(batch)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <span className="text-sm font-medium text-gray-900">Batch #{batch.id}</span>
                          {batch.requires_airline_approval ? (
                            <div title="Requires airline approval">
                              <Building className="w-4 h-4 text-orange-500" />
                            </div>
                          ) : (
                            <div title="Cresent user can approve">
                              <User className="w-4 h-4 text-blue-500" />
                            </div>
                          )}
                          <span className="text-xs px-2 py-1 rounded-full bg-yellow-100 text-yellow-800">
                            {batch.total_variants} variants
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mt-1 truncate">
                          {batch.original_post_content}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          Created: {new Date(batch.created_at).toLocaleString()}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(batch.batch_status)}
                        <ExternalLink className="w-4 h-4 text-gray-400" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ManualReview;
