import { useState, useEffect } from 'react'
import Head from 'next/head'
import { withAuth, useAuth, useRoleAccess } from '../contexts/AuthContext'
import Navigation from '../components/Navigation'
import {
  MessageSquare,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  ThumbsUp,
  ThumbsDown,
  AlertTriangle,
  Filter,
  Search,
  RefreshCw
} from 'lucide-react'

interface ResponseVariant {
  id: string
  response_text: string
  confidence_score: number
  tone: string
  selected?: boolean
}

interface PendingResponse {
  id: string
  original_post: {
    content: string
    platform: string
    sentiment: 'positive' | 'negative' | 'neutral'
    author: string
    timestamp: string
    media_urls?: string[]
  }
  response_variants: ResponseVariant[]
  status: 'pending' | 'approved' | 'rejected'
  created_at: string
  requires_airline_approval?: boolean
}

function ResponseManagement() {
  const { user, logout } = useAuth()
  const { canApproveResponses, canViewAnalytics, isAdmin } = useRoleAccess()
  const [pendingResponses, setPendingResponses] = useState<PendingResponse[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [showReviewModal, setShowReviewModal] = useState(false)
  const [selectedResponse, setSelectedResponse] = useState<PendingResponse | null>(null)
  const [selectedVariant, setSelectedVariant] = useState<string | null>(null)

  useEffect(() => {
    loadPendingResponses()
  }, [])

  const loadPendingResponses = async () => {
    try {
      setLoading(true)

      // Try multiple endpoints to get data
      let responses = []

      // First try to get pending responses
      try {
        const pendingResponse = await fetch('/api/v1/responses/?status=pending&limit=100', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('session_token')}`
          }
        })

        if (pendingResponse.ok) {
          const pendingData = await pendingResponse.json()
          responses = pendingData.responses || []
        }
      } catch (e) {
        console.log('Pending responses endpoint not available')
      }

      // If no pending responses, try to get all responses
      if (responses.length === 0) {
        try {
          const allResponse = await fetch('/api/v1/responses/', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('session_token')}`
            }
          })

          if (allResponse.ok) {
            const allData = await allResponse.json()
            responses = (allData.responses || []).slice(0, 10) // Limit to 10 for demo
          }
        } catch (e) {
          console.log('All responses endpoint not available')
        }
      }

      // If still no data, create mock data with 4 response variants
      if (responses.length === 0) {
        responses = [
          {
            id: 'mock-1',
            social_post_id: 'post-1',
            response_variants: [
              {
                id: 'variant-1-1',
                response_text: 'Thank you for reaching out! We understand your concern and are here to help. Our team will review your case and get back to you within 24 hours.',
                confidence_score: 0.92,
                tone: 'professional'
              },
              {
                id: 'variant-1-2',
                response_text: 'Hi there! Thanks for letting us know about this issue. We really appreciate your feedback and will look into this right away!',
                confidence_score: 0.88,
                tone: 'friendly'
              },
              {
                id: 'variant-1-3',
                response_text: 'We sincerely apologize for the inconvenience. Your concern has been escalated to our priority support team for immediate resolution.',
                confidence_score: 0.85,
                tone: 'formal'
              },
              {
                id: 'variant-1-4',
                response_text: 'Hey! Sorry to hear about this trouble. Let us fix this for you ASAP - our team is on it! 🚀',
                confidence_score: 0.78,
                tone: 'casual'
              }
            ],
            status: 'pending',
            created_at: new Date().toISOString(),
            requires_airline_approval: false // No airline keywords in this post
          },
          {
            id: 'mock-2',
            social_post_id: 'post-2',
            response_variants: [
              {
                id: 'variant-2-1',
                response_text: 'We apologize for the inconvenience you experienced. Your feedback is valuable to us and we are taking immediate action to resolve this issue.',
                confidence_score: 0.90,
                tone: 'professional'
              },
              {
                id: 'variant-2-2',
                response_text: 'So sorry about the app issues! Our tech team is working on a fix right now. Thanks for your patience! 💙',
                confidence_score: 0.82,
                tone: 'friendly'
              },
              {
                id: 'variant-2-3',
                response_text: 'We acknowledge the technical difficulties you have encountered and are implementing corrective measures immediately.',
                confidence_score: 0.87,
                tone: 'formal'
              },
              {
                id: 'variant-2-4',
                response_text: 'Ugh, app crashes are the worst! Our devs are on it - should be fixed soon. Thanks for reporting! 🛠️',
                confidence_score: 0.75,
                tone: 'casual'
              }
            ],
            status: 'pending',
            created_at: new Date().toISOString(),
            requires_airline_approval: true // Contains "app" issue - technical complaint
          }
        ]
      }

      // Transform the data to match our interface
      const transformedResponses = await Promise.all(responses.map(async (resp: any) => {
        // Try to fetch the associated social post
        let postData = null
        try {
          const postResponse = await fetch(`/api/v1/posts/${resp.social_post_id}`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('session_token')}`
            }
          })

          if (postResponse.ok) {
            postData = await postResponse.json()
          }
        } catch (e) {
          // Use mock post data if API not available
          postData = {
            content: resp.social_post_id === 'post-1'
              ? 'Flight delayed again! This is unacceptable customer service. #frustrated'
              : 'Your app keeps crashing when I try to book. Please fix this!',
            platform: 'twitter',
            sentiment: resp.social_post_id === 'post-1' ? 'negative' : 'negative',
            author_username: resp.social_post_id === 'post-1' ? 'frustrated_traveler' : 'app_user_123',
            created_at: new Date().toISOString()
          }
        }

        return {
          id: resp.id,
          original_post: {
            content: postData?.content || 'Post content not available',
            platform: postData?.platform || 'twitter',
            sentiment: postData?.sentiment || 'neutral',
            author: postData?.author_username || 'Unknown',
            timestamp: postData?.created_at || resp.created_at,
            media_urls: postData?.media_urls || []
          },
          response_variants: resp.response_variants || [
            {
              id: `${resp.id}-single`,
              response_text: resp.response_text || 'No response text available',
              confidence_score: resp.confidence_score || 0.5,
              tone: 'professional'
            }
          ],
          status: resp.status,
          created_at: resp.created_at,
          requires_airline_approval: resp.requires_airline_approval || false
        }
      }))

      setPendingResponses(transformedResponses)
      console.log('Loaded responses:', transformedResponses)

    } catch (error) {
      console.error('Failed to load pending responses:', error)
      // Set empty array on error
      setPendingResponses([])
    } finally {
      setLoading(false)
    }
  }

  const openReviewModal = (response: PendingResponse) => {
    setSelectedResponse(response)
    setSelectedVariant(null)
    setShowReviewModal(true)
  }

  const closeReviewModal = () => {
    setShowReviewModal(false)
    setSelectedResponse(null)
    setSelectedVariant(null)
  }

  const selectVariant = (variantId: string) => {
    setSelectedVariant(variantId)
  }

  const approveSelectedVariant = async () => {
    if (!selectedResponse || !selectedVariant) return

    try {
      const response = await fetch(`/api/v1/responses/${selectedResponse.id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('session_token')}`
        },
        body: JSON.stringify({
          selected_variant_id: selectedVariant,
          user_role: user?.role
        })
      })

      if (response.ok) {
        alert('Response variant approved successfully!')
        closeReviewModal()
        loadPendingResponses()
      } else {
        alert('Failed to approve response variant')
      }
    } catch (error) {
      console.error('Error approving variant:', error)
      alert('Error approving response variant')
    }
  }

  const rejectResponse = async (responseId: string) => {
    try {
      const response = await fetch(`/api/v1/responses/${responseId}/reject`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('session_token')}`
        }
      })

      if (response.ok) {
        alert('Response rejected successfully!')
        loadPendingResponses()
      } else {
        alert('Failed to reject response')
      }
    } catch (error) {
      console.error('Error rejecting response:', error)
      alert('Error rejecting response')
    }
  }

  const handleApprove = async (responseId: string) => {
    try {
      const response = await fetch(`/api/v1/responses/${responseId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('session_token')}`
        },
        body: JSON.stringify({
          approved: true,
          admin_notes: 'Approved via Response Management',
          publish_immediately: false
        })
      })

      if (response.ok) {
        setPendingResponses(prev => prev.filter(r => r.id !== responseId))
        // Show success message
        alert('Response approved successfully!')
      } else {
        alert('Failed to approve response')
      }
    } catch (error) {
      console.error('Failed to approve response:', error)
      alert('Error approving response')
    }
  }

  const handleReject = async (responseId: string) => {
    const reason = prompt('Please provide a reason for rejection:')
    if (!reason) return

    try {
      const response = await fetch(`/api/v1/responses/${responseId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('session_token')}`
        },
        body: JSON.stringify({
          approved: false,
          admin_notes: reason,
          publish_immediately: false
        })
      })

      if (response.ok) {
        setPendingResponses(prev => prev.filter(r => r.id !== responseId))
        // Show success message
        alert('Response rejected successfully!')
      } else {
        alert('Failed to reject response')
      }
    } catch (error) {
      console.error('Failed to reject response:', error)
      alert('Error rejecting response')
    }
  }

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'bg-green-100 text-green-800'
      case 'negative': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getConfidenceColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600'
    if (score >= 0.6) return 'text-yellow-600'
    return 'text-red-600'
  }

  const filteredResponses = pendingResponses.filter(response => {
    const matchesFilter = filter === 'all' || response.original_post.sentiment === filter
    const matchesSearch = response.original_post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         response.response_variants.some(variant =>
                           variant.response_text.toLowerCase().includes(searchTerm.toLowerCase())
                         )
    return matchesFilter && matchesSearch
  })

  return (
    <>
      <Head>
        <title>Response Management - Agentic ORM</title>
        <meta name="description" content="Manage and approve AI-generated responses" />
      </Head>

      <style jsx>{`
        .card { 
          background: white; 
          border-radius: 0.5rem; 
          box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); 
          border: 1px solid #e5e7eb; 
          padding: 1.5rem; 
        }
        .btn-primary { 
          background: #2563eb; 
          color: white; 
          font-weight: 500; 
          padding: 0.5rem 1rem; 
          border-radius: 0.5rem; 
          transition: all 0.2s; 
          border: none;
          cursor: pointer;
        }
        .btn-primary:hover { 
          background: #1d4ed8; 
        }
        .btn-success { 
          background: #059669; 
          color: white; 
          font-weight: 500; 
          padding: 0.5rem 1rem; 
          border-radius: 0.5rem; 
          transition: all 0.2s; 
          border: none;
          cursor: pointer;
        }
        .btn-success:hover { 
          background: #047857; 
        }
        .btn-danger { 
          background: #dc2626; 
          color: white; 
          font-weight: 500; 
          padding: 0.5rem 1rem; 
          border-radius: 0.5rem; 
          transition: all 0.2s; 
          border: none;
          cursor: pointer;
        }
        .btn-danger:hover { 
          background: #b91c1c; 
        }
        .app-container {
          display: flex;
          flex-direction: column;
          min-height: 100vh;
        }
        .app-body {
          margin-top: 5rem;
          min-height: calc(100vh - 5rem);
        }
        .main-content {
          flex: 1;
          background: #f9fafb;
          width: 100%;
        }
      `}</style>

      <div className="app-container">
        <Navigation currentPage="response-management" />

        {/* Main Content */}
        <div className="app-body">
          <main className="main-content">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              {/* Page Header */}
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Response Management</h2>
                
                {/* Filters and Search */}
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <Filter className="w-4 h-4 text-gray-500" />
                      <select
                        value={filter}
                        onChange={(e) => setFilter(e.target.value)}
                        className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
                      >
                        <option value="all">All Sentiments</option>
                        <option value="positive">Positive</option>
                        <option value="neutral">Neutral</option>
                        <option value="negative">Negative</option>
                      </select>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Search className="w-4 h-4 text-gray-500" />
                      <input
                        type="text"
                        placeholder="Search responses..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="border border-gray-300 rounded-lg px-3 py-2 text-sm w-64"
                      />
                    </div>
                  </div>
                  
                  <button
                    onClick={loadPendingResponses}
                    className="btn-primary flex items-center space-x-2"
                  >
                    <RefreshCw className="w-4 h-4" />
                    <span>Refresh</span>
                  </button>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                  <div className="card">
                    <div className="flex items-center">
                      <div className="p-2 bg-yellow-100 rounded-lg mr-3">
                        <Clock className="w-5 h-5 text-yellow-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Pending</p>
                        <p className="text-xl font-bold">{filteredResponses.length}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="card">
                    <div className="flex items-center">
                      <div className="p-2 bg-green-100 rounded-lg mr-3">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">High Confidence</p>
                        <p className="text-xl font-bold">{filteredResponses.filter(r => r.confidence_score >= 0.8).length}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="card">
                    <div className="flex items-center">
                      <div className="p-2 bg-red-100 rounded-lg mr-3">
                        <AlertTriangle className="w-5 h-5 text-red-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Needs Review</p>
                        <p className="text-xl font-bold">{filteredResponses.filter(r => r.confidence_score < 0.6).length}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="card">
                    <div className="flex items-center">
                      <div className="p-2 bg-blue-100 rounded-lg mr-3">
                        <MessageSquare className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Total Today</p>
                        <p className="text-xl font-bold">{pendingResponses.length}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Response List */}
              {loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-4 text-gray-600">Loading responses...</p>
                </div>
              ) : filteredResponses.length === 0 ? (
                <div className="text-center py-12">
                  <MessageSquare className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No pending responses</h3>
                  <p className="text-gray-500">All responses have been reviewed or no new posts detected.</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {filteredResponses.map((response) => (
                    <div key={response.id} className="card">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Original Post */}
                        <div>
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="text-lg font-medium text-gray-900">Original Post</h4>
                            <div className="flex items-center space-x-2">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSentimentColor(response.original_post.sentiment)}`}>
                                {response.original_post.sentiment}
                              </span>
                              <span className="text-xs text-gray-500">{response.original_post.platform}</span>
                            </div>
                          </div>
                          <div className="bg-gray-50 rounded-lg p-4 mb-3">
                            <p className="text-gray-800">{response.original_post.content}</p>
                          </div>
                          <div className="flex items-center justify-between text-sm text-gray-500">
                            <span>@{response.original_post.author}</span>
                            <span>{new Date(response.original_post.timestamp).toLocaleString()}</span>
                          </div>
                        </div>

                        {/* Generated Response */}
                        <div>
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="text-lg font-medium text-gray-900">AI Generated Response</h4>
                            <div className="flex items-center space-x-2">
                              <span className={`text-sm font-medium ${getConfidenceColor(response.response_variants[0]?.confidence_score || 0)}`}>
                                {Math.round((response.response_variants[0]?.confidence_score || 0) * 100)}% confidence
                              </span>
                            </div>
                          </div>
                          <div className="bg-blue-50 rounded-lg p-4 mb-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="text-sm text-gray-600 mb-2">
                                  {response.response_variants.length} response variants generated
                                </p>
                                <p className="text-gray-800 font-medium">
                                  {response.requires_airline_approval ? '✈️ Requires Airline Approval' : '🏢 Cresent Review'}
                                </p>
                              </div>
                              <button
                                onClick={() => openReviewModal(response)}
                                className="btn-primary flex items-center space-x-2"
                              >
                                <Eye className="w-4 h-4" />
                                <span>Review {response.response_variants.length} Variants</span>
                              </button>
                            </div>
                          </div>

                          {/* Quick Actions */}
                          <div className="flex items-center space-x-3">
                            <button
                              onClick={() => rejectResponse(response.id)}
                              className="btn-danger flex items-center space-x-2"
                            >
                              <XCircle className="w-4 h-4" />
                              <span>Reject All</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </main>
        </div>

        {/* Response Review Modal */}
        {showReviewModal && selectedResponse && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-screen overflow-y-auto">
              <div className="p-6">
                {/* Modal Header */}
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-gray-900">Review Response Variants</h2>
                  <button onClick={closeReviewModal} className="text-gray-400 hover:text-gray-600">
                    <XCircle className="w-6 h-6" />
                  </button>
                </div>

                {/* Original Post */}
                <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                  <h3 className="font-semibold text-gray-900 mb-2">Original Post</h3>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-sm text-gray-600">@{selectedResponse.original_post.author}</span>
                    <span className="text-sm text-gray-600">•</span>
                    <span className="text-sm text-gray-600">{selectedResponse.original_post.platform}</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      selectedResponse.original_post.sentiment === 'positive' ? 'bg-green-100 text-green-800' :
                      selectedResponse.original_post.sentiment === 'negative' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {selectedResponse.original_post.sentiment}
                    </span>
                  </div>
                  <p className="text-gray-800">{selectedResponse.original_post.content}</p>
                </div>

                {/* Response Variants */}
                <div className="mb-6">
                  <h3 className="font-semibold text-gray-900 mb-4">Select Response Variant</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {selectedResponse.response_variants.map((variant, index) => (
                      <div
                        key={variant.id}
                        className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                          selectedVariant === variant.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-blue-300'
                        }`}
                        onClick={() => selectVariant(variant.id)}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-900">
                            Variant {index + 1} - {variant.tone}
                          </span>
                          <span className="text-sm text-gray-600">
                            {Math.round(variant.confidence_score * 100)}% confidence
                          </span>
                        </div>
                        <p className="text-gray-800 mb-2">{variant.response_text}</p>
                        {selectedVariant === variant.id && (
                          <div className="flex items-center text-blue-600">
                            <CheckCircle className="w-4 h-4 mr-1" />
                            <span className="text-sm font-medium">Selected</span>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Approval Actions */}
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    {selectedResponse.requires_airline_approval
                      ? '✈️ This response requires airline user approval'
                      : '🏢 This response can be approved by Cresent users'
                    }
                  </div>
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={closeReviewModal}
                      className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={() => rejectResponse(selectedResponse.id)}
                      className="btn-danger flex items-center space-x-2"
                    >
                      <XCircle className="w-4 h-4" />
                      <span>Reject All Variants</span>
                    </button>
                    <button
                      onClick={approveSelectedVariant}
                      disabled={!selectedVariant}
                      className={`btn-success flex items-center space-x-2 ${
                        !selectedVariant ? 'opacity-50 cursor-not-allowed' : ''
                      }`}
                    >
                      <CheckCircle className="w-4 h-4" />
                      <span>Approve Selected</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  )
}

export default withAuth(ResponseManagement)
