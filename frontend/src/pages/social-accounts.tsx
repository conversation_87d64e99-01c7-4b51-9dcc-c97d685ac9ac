import { useState, useEffect } from 'react'
import Head from 'next/head'
import { withAuth, useAuth, useRoleAccess } from '../contexts/AuthContext'
import {
  BrainCircuit,
  Home,
  MessageCircle,
  TrendingUp,
  Users,
  Settings,
  LogOut,
  Plus,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Twitter,
  Facebook,
  Instagram,
  Linkedin,
  RefreshCw,
  Key,
  Globe,
  Activity
} from 'lucide-react'

interface SocialAccount {
  id: string
  platform: 'twitter' | 'facebook' | 'instagram' | 'linkedin'
  username: string
  display_name: string
  status: 'active' | 'inactive' | 'error'
  last_sync: string
  followers_count: number
  posts_monitored: number
  responses_generated: number
  created_at: string
}

function SocialAccounts() {
  const { user, logout } = useAuth()
  const { isAdmin } = useRoleAccess()
  const [accounts, setAccounts] = useState<SocialAccount[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [newAccount, setNewAccount] = useState({
    platform: 'twitter',
    username: '',
    display_name: '',
    platform_user_id: '',
    api_key: '',
    api_secret: '',
    access_token: '',
    access_token_secret: ''
  })

  useEffect(() => {
    loadSocialAccounts()
  }, [])

  const loadSocialAccounts = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/v1/social-accounts', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('session_token')}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setAccounts(data.accounts || [])
      } else {
        // Mock data for demo
        setAccounts([
          {
            id: '1',
            platform: 'twitter',
            username: '@company_support',
            display_name: 'Company Support',
            status: 'active',
            last_sync: '2024-01-15T10:30:00Z',
            followers_count: 15420,
            posts_monitored: 1250,
            responses_generated: 89,
            created_at: '2024-01-01T00:00:00Z'
          },
          {
            id: '2',
            platform: 'facebook',
            username: 'CompanyPage',
            display_name: 'Company Official Page',
            status: 'active',
            last_sync: '2024-01-15T10:25:00Z',
            followers_count: 8930,
            posts_monitored: 567,
            responses_generated: 34,
            created_at: '2024-01-01T00:00:00Z'
          },
          {
            id: '3',
            platform: 'instagram',
            username: '@company_official',
            display_name: 'Company Instagram',
            status: 'error',
            last_sync: '2024-01-14T15:20:00Z',
            followers_count: 23100,
            posts_monitored: 890,
            responses_generated: 12,
            created_at: '2024-01-01T00:00:00Z'
          }
        ])
      }
    } catch (error) {
      console.error('Failed to load social accounts:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddAccount = async () => {
    try {
      // Prepare data in the format expected by the API
      const accountData = {
        platform: newAccount.platform,
        username: newAccount.username,
        display_name: newAccount.display_name || newAccount.username,
        platform_user_id: newAccount.platform_user_id || newAccount.username.replace('@', ''),
        api_credentials: {
          api_key: newAccount.api_key,
          api_secret: newAccount.api_secret,
          access_token: newAccount.access_token,
          access_token_secret: newAccount.access_token_secret
        }
      }

      const response = await fetch('/api/v1/social-accounts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('session_token')}`
        },
        body: JSON.stringify(accountData)
      })

      if (response.ok) {
        setShowAddModal(false)
        setNewAccount({
          platform: 'twitter',
          username: '',
          display_name: '',
          platform_user_id: '',
          api_key: '',
          api_secret: '',
          access_token: '',
          access_token_secret: ''
        })
        loadSocialAccounts()
        alert('Social account added successfully!')
      } else {
        const errorData = await response.json()
        console.error('API Error:', errorData)
        alert(`Failed to add account: ${errorData.detail || 'Unknown error'}`)
      }
    } catch (error) {
      console.error('Failed to add account:', error)
      alert('Failed to add account. Please try again.')
    }
  }

  const handleDeleteAccount = async (accountId: string) => {
    if (!confirm('Are you sure you want to delete this account?')) return

    try {
      const response = await fetch(`/api/v1/social-accounts/${accountId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('session_token')}`
        }
      })

      if (response.ok) {
        setAccounts(prev => prev.filter(acc => acc.id !== accountId))
      }
    } catch (error) {
      console.error('Failed to delete account:', error)
    }
  }

  const handleSyncAccount = async (accountId: string) => {
    try {
      const response = await fetch(`/api/v1/social-accounts/${accountId}/sync`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('session_token')}`
        }
      })

      if (response.ok) {
        loadSocialAccounts()
      }
    } catch (error) {
      console.error('Failed to sync account:', error)
    }
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'twitter': return <Twitter className="w-5 h-5" />
      case 'facebook': return <Facebook className="w-5 h-5" />
      case 'instagram': return <Instagram className="w-5 h-5" />
      case 'linkedin': return <Linkedin className="w-5 h-5" />
      default: return <Globe className="w-5 h-5" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'inactive': return 'bg-gray-100 text-gray-800'
      case 'error': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4" />
      case 'inactive': return <XCircle className="w-4 h-4" />
      case 'error': return <AlertTriangle className="w-4 h-4" />
      default: return <XCircle className="w-4 h-4" />
    }
  }

  return (
    <>
      <Head>
        <title>Social Accounts - Agentic ORM</title>
        <meta name="description" content="Manage connected social media accounts" />
      </Head>

      <style jsx>{`
        .card {
          background: white;
          border-radius: 0.5rem;
          box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
          border: 1px solid #e5e7eb;
          padding: 1.5rem;
        }
        .btn-primary {
          background: #2563eb;
          color: white;
          font-weight: 500;
          padding: 0.5rem 1rem;
          border-radius: 0.5rem;
          transition: all 0.2s;
          border: none;
          cursor: pointer;
        }
        .btn-primary:hover {
          background: #1d4ed8;
        }
        .btn-secondary {
          background: #6b7280;
          color: white;
          font-weight: 500;
          padding: 0.5rem 1rem;
          border-radius: 0.5rem;
          transition: all 0.2s;
          border: none;
          cursor: pointer;
        }
        .btn-secondary:hover {
          background: #4b5563;
        }
        .btn-danger {
          background: #dc2626;
          color: white;
          font-weight: 500;
          padding: 0.5rem 1rem;
          border-radius: 0.5rem;
          transition: all 0.2s;
          border: none;
          cursor: pointer;
        }
        .btn-danger:hover {
          background: #b91c1c;
        }
        .app-container {
          display: flex;
          flex-direction: column;
          min-height: 100vh;
        }
        .app-header {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          z-index: 50;
          background: white;
          border-bottom: 1px solid #e5e7eb;
          box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
          height: 5rem;
        }
        .app-body {
          margin-top: 5rem;
          min-height: calc(100vh - 5rem);
        }
        .top-nav {
          background: white;
          border-bottom: 1px solid #e5e7eb;
          height: 3rem;
        }
        .top-nav-menu {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          height: 100%;
          max-width: 80rem;
          margin: 0 auto;
          padding: 0 1rem;
        }
        .top-nav-item {
          position: relative;
          display: flex;
          align-items: center;
          padding: 0.75rem 1.5rem;
          color: #374151;
          font-weight: 500;
          text-decoration: none;
          transition: all 0.2s ease-in-out;
          cursor: pointer;
          border-radius: 0.5rem;
          margin: 0 0.25rem;
        }
        .top-nav-item:hover {
          background-color: #f3f4f6;
          color: #1d4ed8;
        }
        .top-nav-item.active {
          background-color: #dbeafe;
          color: #1d4ed8;
          font-weight: 600;
        }
        .top-nav-item-icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
          flex-shrink: 0;
        }
        .main-content {
          flex: 1;
          background: #f9fafb;
          width: 100%;
        }
        .modal {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 100;
        }
        .modal-content {
          background: white;
          border-radius: 0.5rem;
          padding: 2rem;
          max-width: 500px;
          width: 90%;
          max-height: 90vh;
          overflow-y: auto;
        }
      `}</style>

      <div className="app-container">
        {/* Header */}
        <header className="app-header">
          {/* Top Header Bar */}
          <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 h-16 border-b border-gray-200">
            <div className="flex justify-between items-center h-full">
              <div className="flex items-center">
                <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">
                  🤖 Agentic ORM
                </h1>
              </div>
              <div className="flex items-center space-x-4">
                <div className="hidden lg:flex items-center space-x-4">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                    System Online
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-medium">
                        {user?.full_name?.charAt(0) || user?.username?.charAt(0) || 'U'}
                      </span>
                    </div>
                    <div className="text-sm">
                      <p className="font-medium text-gray-900">{user?.full_name || user?.username}</p>
                      <p className="text-gray-500 capitalize">{user?.role?.replace('_', ' ')}</p>
                    </div>
                    <button
                      onClick={logout}
                      className="text-gray-500 hover:text-gray-700 ml-2"
                    >
                      <LogOut className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Top Navigation */}
          <div className="hidden lg:block top-nav">
            <nav className="top-nav-menu">
              <a href="/" className="top-nav-item">
                <Home className="top-nav-item-icon" />
                <span>Dashboard</span>
              </a>

              <a href="/response-management" className="top-nav-item">
                <MessageCircle className="top-nav-item-icon" />
                <span>Response Management</span>
              </a>

              <div className="top-nav-item active">
                <Users className="top-nav-item-icon" />
                <span>Social Accounts</span>
              </div>

              <a href="/analytics" className="top-nav-item">
                <TrendingUp className="top-nav-item-icon" />
                <span>Analytics</span>
              </a>

              {isAdmin() && (
                <a href="/admin" className="top-nav-item">
                  <Settings className="top-nav-item-icon" />
                  <span>Administration</span>
                </a>
              )}
            </nav>
          </div>
        </header>

        {/* Main Content */}
        <div className="app-body">
          <main className="main-content">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              {/* Page Header */}
              <div className="mb-8">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold text-gray-900">Social Accounts</h2>
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={loadSocialAccounts}
                      className="btn-secondary flex items-center space-x-2"
                    >
                      <RefreshCw className="w-4 h-4" />
                      <span>Refresh</span>
                    </button>
                    {isAdmin() && (
                      <button
                        onClick={() => setShowAddModal(true)}
                        className="btn-primary flex items-center space-x-2"
                      >
                        <Plus className="w-4 h-4" />
                        <span>Add Account</span>
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div className="card">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg mr-3">
                      <Users className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Total Accounts</p>
                      <p className="text-xl font-bold">{accounts.length}</p>
                    </div>
                  </div>
                </div>

                <div className="card">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-lg mr-3">
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Active</p>
                      <p className="text-xl font-bold">{accounts.filter(acc => acc.status === 'active').length}</p>
                    </div>
                  </div>
                </div>

                <div className="card">
                  <div className="flex items-center">
                    <div className="p-2 bg-yellow-100 rounded-lg mr-3">
                      <Activity className="w-5 h-5 text-yellow-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Total Followers</p>
                      <p className="text-xl font-bold">{accounts.reduce((sum, acc) => sum + acc.followers_count, 0).toLocaleString()}</p>
                    </div>
                  </div>
                </div>

                <div className="card">
                  <div className="flex items-center">
                    <div className="p-2 bg-purple-100 rounded-lg mr-3">
                      <MessageCircle className="w-5 h-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Responses Generated</p>
                      <p className="text-xl font-bold">{accounts.reduce((sum, acc) => sum + acc.responses_generated, 0)}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Accounts List */}
              {loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-4 text-gray-600">Loading accounts...</p>
                </div>
              ) : accounts.length === 0 ? (
                <div className="text-center py-12">
                  <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No social accounts connected</h3>
                  <p className="text-gray-500 mb-4">Connect your social media accounts to start monitoring and responding to posts.</p>
                  {isAdmin() && (
                    <button
                      onClick={() => setShowAddModal(true)}
                      className="btn-primary"
                    >
                      Add Your First Account
                    </button>
                  )}
                </div>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {accounts.map((account) => (
                    <div key={account.id} className="card">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-gray-100 rounded-lg">
                            {getPlatformIcon(account.platform)}
                          </div>
                          <div>
                            <h3 className="text-lg font-medium text-gray-900">{account.display_name}</h3>
                            <p className="text-sm text-gray-500">{account.username}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(account.status)}`}>
                            {getStatusIcon(account.status)}
                            <span className="ml-1 capitalize">{account.status}</span>
                          </span>
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4 mb-4">
                        <div className="text-center">
                          <p className="text-lg font-bold text-gray-900">{account.followers_count.toLocaleString()}</p>
                          <p className="text-xs text-gray-500">Followers</p>
                        </div>
                        <div className="text-center">
                          <p className="text-lg font-bold text-gray-900">{account.posts_monitored}</p>
                          <p className="text-xs text-gray-500">Posts Monitored</p>
                        </div>
                        <div className="text-center">
                          <p className="text-lg font-bold text-gray-900">{account.responses_generated}</p>
                          <p className="text-xs text-gray-500">Responses</p>
                        </div>
                      </div>

                      <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                        <span>Last sync: {new Date(account.last_sync).toLocaleString()}</span>
                        <span>Added: {new Date(account.created_at).toLocaleDateString()}</span>
                      </div>

                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleSyncAccount(account.id)}
                          className="btn-secondary flex items-center space-x-1 text-sm"
                        >
                          <RefreshCw className="w-3 h-3" />
                          <span>Sync</span>
                        </button>
                        {isAdmin() && (
                          <>
                            <button className="btn-secondary flex items-center space-x-1 text-sm">
                              <Edit className="w-3 h-3" />
                              <span>Edit</span>
                            </button>
                            <button
                              onClick={() => handleDeleteAccount(account.id)}
                              className="btn-danger flex items-center space-x-1 text-sm"
                            >
                              <Trash2 className="w-3 h-3" />
                              <span>Delete</span>
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </main>
        </div>

        {/* Add Account Modal */}
        {showAddModal && (
          <div className="modal">
            <div className="modal-content">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Add Social Account</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Platform</label>
                  <select
                    value={newAccount.platform}
                    onChange={(e) => setNewAccount(prev => ({ ...prev, platform: e.target.value }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  >
                    <option value="twitter">Twitter</option>
                    <option value="facebook">Facebook</option>
                    <option value="instagram">Instagram</option>
                    <option value="linkedin">LinkedIn</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Username</label>
                  <input
                    type="text"
                    value={newAccount.username}
                    onChange={(e) => setNewAccount(prev => ({ ...prev, username: e.target.value }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                    placeholder="@username or page name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">API Key</label>
                  <input
                    type="password"
                    value={newAccount.api_key}
                    onChange={(e) => setNewAccount(prev => ({ ...prev, api_key: e.target.value }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                    placeholder="Enter API key"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">API Secret</label>
                  <input
                    type="password"
                    value={newAccount.api_secret}
                    onChange={(e) => setNewAccount(prev => ({ ...prev, api_secret: e.target.value }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                    placeholder="Enter API secret"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Platform User ID</label>
                  <input
                    type="text"
                    value={newAccount.platform_user_id}
                    onChange={(e) => setNewAccount(prev => ({ ...prev, platform_user_id: e.target.value }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                    placeholder="Platform user ID (required)"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Display Name</label>
                  <input
                    type="text"
                    value={newAccount.display_name}
                    onChange={(e) => setNewAccount(prev => ({ ...prev, display_name: e.target.value }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                    placeholder="Display name (optional)"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-3 mt-6">
                <button
                  onClick={handleAddAccount}
                  className="btn-primary"
                >
                  Add Account
                </button>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="btn-secondary"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  )
}

export default withAuth(SocialAccounts)
