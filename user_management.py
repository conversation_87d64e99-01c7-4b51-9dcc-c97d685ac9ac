"""
Legacy User Management System
Provides compatibility with the existing authentication system
"""

import hashlib
import secrets
import time
from typing import Dict, Optional, Any
import json
import os


class UserManager:
    """
    User management system that integrates with PostgreSQL database
    Provides session management and authentication
    """
    
    def __init__(self):
        self.session_file = "sessions.json"
        self.sessions = self._load_sessions()  # Persistent session storage
        self.session_timeout = 24 * 60 * 60  # 24 hours
        
        # Default users for the system
        self.users = {
            "cresent_reviewer": {
                "username": "cresent_reviewer",
                "email": "<EMAIL>",
                "full_name": "Cresent Reviewer",
                "role": "cresent_user",
                "status": "active",
                "password_hash": self._hash_password("cresent123"),  # Default password
                "can_approve_responses": True,
                "can_escalate": True,
                "can_view_analytics": True,
                "max_approval_amount": 1000.00
            },
            "airline_approver": {
                "username": "airline_approver",
                "email": "<EMAIL>",
                "full_name": "Airline Approver",
                "role": "airlines_user",
                "status": "active",
                "password_hash": self._hash_password("airline123"),  # Default password
                "can_approve_responses": True,
                "can_escalate": False,
                "can_view_analytics": True,
                "max_approval_amount": 5000.00
            },
            "admin": {
                "username": "admin",
                "email": "<EMAIL>",
                "full_name": "System Administrator",
                "role": "admin",
                "status": "active",
                "password_hash": self._hash_password("admin123"),  # Default password
                "can_approve_responses": True,
                "can_escalate": True,
                "can_view_analytics": True,
                "max_approval_amount": 10000.00
            }
        }
    
    def _hash_password(self, password: str) -> str:
        """Hash a password using SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()

    def _load_sessions(self) -> Dict[str, Any]:
        """Load sessions from file"""
        try:
            if os.path.exists(self.session_file):
                with open(self.session_file, 'r') as f:
                    return json.load(f)
        except Exception:
            pass
        return {}

    def _save_sessions(self):
        """Save sessions to file"""
        try:
            with open(self.session_file, 'w') as f:
                json.dump(self.sessions, f)
        except Exception:
            pass
    
    def authenticate_user(self, username: str, password: str) -> Dict[str, Any]:
        """
        Authenticate a user with username and password
        
        Args:
            username: The username
            password: The password
            
        Returns:
            Dict with success status and error message if applicable
        """
        if username not in self.users:
            return {
                "success": False,
                "error": "Invalid username or password"
            }
        
        user = self.users[username]
        password_hash = self._hash_password(password)
        
        if user["password_hash"] != password_hash:
            return {
                "success": False,
                "error": "Invalid username or password"
            }
        
        if user["status"] != "active":
            return {
                "success": False,
                "error": "User account is inactive"
            }
        
        return {"success": True}
    
    def create_session(self, username: str) -> Optional[str]:
        """
        Create a session token for a user
        
        Args:
            username: The username
            
        Returns:
            Session token string or None if user not found
        """
        if username not in self.users:
            return None
        
        # Generate session token
        session_token = secrets.token_urlsafe(32)
        
        # Store session
        self.sessions[session_token] = {
            "username": username,
            "created_at": time.time(),
            "last_accessed": time.time()
        }
        self._save_sessions()

        return session_token
    
    def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """
        Get user data by username
        
        Args:
            username: The username
            
        Returns:
            User data dictionary or None if not found
        """
        if username not in self.users:
            return None
        
        user = self.users[username].copy()
        # Remove password hash from returned data
        user.pop("password_hash", None)
        return user
    
    def get_user_by_session(self, session_token: str) -> Optional[Dict[str, Any]]:
        """
        Get user data by session token
        
        Args:
            session_token: The session token
            
        Returns:
            User data dictionary or None if session invalid/expired
        """
        if session_token not in self.sessions:
            return None
        
        session = self.sessions[session_token]
        
        # Check if session is expired
        if time.time() - session["created_at"] > self.session_timeout:
            del self.sessions[session_token]
            self._save_sessions()
            return None

        # Update last accessed time
        session["last_accessed"] = time.time()
        self._save_sessions()
        
        # Get user data
        username = session["username"]
        return self.get_user_by_username(username)
    
    def invalidate_session(self, session_token: str) -> bool:
        """
        Invalidate a session token
        
        Args:
            session_token: The session token to invalidate
            
        Returns:
            True if session was found and invalidated, False otherwise
        """
        if session_token in self.sessions:
            del self.sessions[session_token]
            self._save_sessions()
            return True
        return False
    
    def cleanup_expired_sessions(self):
        """Remove expired sessions"""
        current_time = time.time()
        expired_tokens = [
            token for token, session in self.sessions.items()
            if current_time - session["created_at"] > self.session_timeout
        ]
        
        for token in expired_tokens:
            del self.sessions[token]
    
    def get_all_users(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all users (without password hashes)
        
        Returns:
            Dictionary of all users
        """
        result = {}
        for username, user_data in self.users.items():
            user = user_data.copy()
            user.pop("password_hash", None)
            result[username] = user
        return result
    
    def add_user(self, username: str, email: str, full_name: str, role: str, password: str) -> bool:
        """
        Add a new user
        
        Args:
            username: The username
            email: The email address
            full_name: The full name
            role: The user role
            password: The password
            
        Returns:
            True if user was added, False if username already exists
        """
        if username in self.users:
            return False
        
        self.users[username] = {
            "username": username,
            "email": email,
            "full_name": full_name,
            "role": role,
            "status": "active",
            "password_hash": self._hash_password(password),
            "can_approve_responses": role in ["cresent_user", "airlines_user", "admin"],
            "can_escalate": role in ["cresent_user", "admin"],
            "can_view_analytics": True
        }
        return True
